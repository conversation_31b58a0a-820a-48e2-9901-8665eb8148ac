-- Add new social media platform columns to hero_section table

-- Facebook
ALTER TABLE `hero_section` ADD COLUMN `facebook_url` VARCHAR(255) NULL AFTER `twitter_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `facebook_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `facebook_url`;

-- Instagram
ALTER TABLE `hero_section` ADD COLUMN `instagram_url` VARCHAR(255) NULL AFTER `facebook_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `instagram_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `instagram_url`;

-- Dribbble
ALTER TABLE `hero_section` ADD COLUMN `dribbble_url` VARCHAR(255) NULL AFTER `instagram_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `dribbble_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `dribbble_url`;

-- <PERSON>hance
ALTER TABLE `hero_section` ADD COLUMN `behance_url` VARCHAR(255) NULL AFTER `dribbble_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `behance_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `behance_url`;

-- Fiverr
ALTER TABLE `hero_section` ADD COLUMN `fiverr_url` VARCHAR(255) NULL AFTER `behance_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `fiverr_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `fiverr_url`;

-- Upwork
ALTER TABLE `hero_section` ADD COLUMN `upwork_url` VARCHAR(255) NULL AFTER `fiverr_enabled`;
ALTER TABLE `hero_section` ADD COLUMN `upwork_enabled` TINYINT(1) NOT NULL DEFAULT 1 AFTER `upwork_url`;

-- Update existing record with default values
UPDATE `hero_section` SET 
    `facebook_url` = 'https://facebook.com',
    `facebook_enabled` = 1,
    `instagram_url` = 'https://instagram.com',
    `instagram_enabled` = 1,
    `dribbble_url` = 'https://dribbble.com',
    `dribbble_enabled` = 1,
    `behance_url` = 'https://behance.net',
    `behance_enabled` = 1,
    `fiverr_url` = 'https://fiverr.com',
    `fiverr_enabled` = 1,
    `upwork_url` = 'https://upwork.com',
    `upwork_enabled` = 1
WHERE `id` = 1;
