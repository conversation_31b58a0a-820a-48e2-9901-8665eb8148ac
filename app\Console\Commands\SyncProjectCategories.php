<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProjectsManagement;
use App\Models\Project;

class SyncProjectCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'projects:sync-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync project categories from projects table to projects_management table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing project categories to projects_management table...');

        // Get all unique categories from projects table
        $projectCategories = Project::getAllUniqueCategories();
        $this->info('Found categories in projects table: ' . implode(', ', $projectCategories ?: ['(none)']));

        // Get or create projects_management record
        $contentManagement = ProjectsManagement::first();
        if (!$contentManagement) {
            $contentManagement = ProjectsManagement::getOrCreate();
            $this->info('Created new projects_management record.');
        }

        // Update filter_categories with current project categories
        $contentManagement->update([
            'filter_categories' => $projectCategories
        ]);

        $this->info('✅ Successfully synced categories to projects_management table!');
        $this->info('📋 Categories now in projects_management: ' . implode(', ', $projectCategories ?: ['(none)']));
        $this->info('🎯 Frontend will now show these categories on projects page.');
        $this->info('💡 Add new projects from admin panel to automatically update categories.');
        
        return 0;
    }
}
