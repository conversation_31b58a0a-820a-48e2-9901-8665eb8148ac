<?php

namespace App\Console\Commands;

use App\Models\HeroSection;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class UpdateProfileAvatar extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'profile:update-avatar';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update hero section avatar to use the Profile.png image';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the hero section
        $heroSection = HeroSection::getOrCreate();

        // Check if public/images/Profile.png exists
        $sourcePath = public_path('images/Profile.png');
        if (!File::exists($sourcePath)) {
            $this->error('Profile.png not found in public/images directory');
            return 1;
        }

        // Create the avatars directory if it doesn't exist
        $avatarsDir = storage_path('app/public/avatars');
        if (!File::exists($avatarsDir)) {
            File::makeDirectory($avatarsDir, 0755, true);
            $this->info('Created avatars directory');
        }

        // Copy the Profile.png to storage/app/public/avatars
        $destinationPath = 'avatars/profile.png';
        File::copy($sourcePath, storage_path('app/public/' . $destinationPath));
        
        // Update the hero section record
        $heroSection->avatar = $destinationPath;
        $heroSection->save();

        $this->info('Hero section avatar updated successfully');
        
        return 0;
    }
} 