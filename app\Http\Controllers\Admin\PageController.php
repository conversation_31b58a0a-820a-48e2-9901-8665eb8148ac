<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PageController extends Controller
{
    /**
     * Display a listing of pages
     */
    public function index()
    {
        $pages = Page::orderBy('order')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($page) {
                return [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'status' => $page->status,
                    'is_featured' => $page->is_featured,
                    'published_at' => $page->published_at?->format('M d, Y'),
                    'created_at' => $page->created_at->format('M d, Y'),
                    'excerpt' => $page->excerpt,
                    'featured_image' => $page->featured_image ? Storage::url($page->featured_image) : null,
                    'url' => $page->url,
                ];
            });

        return Inertia::render('admin/pages', [
            'pages' => $pages,
        ]);
    }

    /**
     * Show the form for creating a new page
     */
    public function create()
    {
        return Inertia::render('admin/pages/create');
    }

    /**
     * Store a newly created page
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'content' => 'required|string',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'page_layout' => 'required|string|in:default',
            'status' => 'required|string|in:draft,published',
            'publish_immediately' => 'boolean',
            'is_featured' => 'boolean',
            'featured_image' => 'nullable|image|max:2048',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        } else {
            $validated['slug'] = Str::slug($validated['slug']);
        }

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Page::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')->store('pages', 'public');
        }

        // Set order to be after the last page
        $maxOrder = Page::max('order') ?? 0;
        $validated['order'] = $maxOrder + 1;

        $page = Page::create($validated);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified page
     */
    public function show(Page $page)
    {
        return Inertia::render('admin/pages/show', [
            'page' => [
                'id' => $page->id,
                'title' => $page->title,
                'slug' => $page->slug,
                'content' => $page->content,
                'meta_description' => $page->meta_description,
                'meta_keywords' => $page->meta_keywords,
                'featured_image' => $page->featured_image ? Storage::url($page->featured_image) : null,
                'page_layout' => $page->page_layout,
                'status' => $page->status,
                'is_featured' => $page->is_featured,
                'published_at' => $page->published_at?->format('Y-m-d\TH:i'),
                'created_at' => $page->created_at->format('M d, Y H:i'),
                'updated_at' => $page->updated_at->format('M d, Y H:i'),
                'url' => $page->url,
            ],
        ]);
    }

    /**
     * Show the form for editing the specified page
     */
    public function edit(Page $page)
    {
        return Inertia::render('admin/pages/edit', [
            'page' => [
                'id' => $page->id,
                'title' => $page->title,
                'slug' => $page->slug,
                'content' => $page->content,
                'meta_description' => $page->meta_description,
                'meta_keywords' => $page->meta_keywords,
                'featured_image' => $page->featured_image ? Storage::url($page->featured_image) : null,
                'page_layout' => $page->page_layout,
                'status' => $page->status,
                'publish_immediately' => false, // Always false for editing
                'is_featured' => $page->is_featured,
                'published_at' => $page->published_at?->format('Y-m-d\TH:i'),
            ],
        ]);
    }

    /**
     * Update the specified page
     */
    public function update(Request $request, Page $page)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $page->id,
            'content' => 'required|string',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'page_layout' => 'required|string|in:default',
            'status' => 'required|string|in:draft,published',
            'publish_immediately' => 'boolean',
            'is_featured' => 'boolean',
            'featured_image' => 'nullable|image|max:2048',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        } else {
            $validated['slug'] = Str::slug($validated['slug']);
        }

        // Ensure slug is unique (excluding current page)
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Page::where('slug', $validated['slug'])->where('id', '!=', $page->id)->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($page->featured_image) {
                Storage::disk('public')->delete($page->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')->store('pages', 'public');
        }

        $page->update($validated);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified page
     */
    public function destroy(Page $page)
    {
        // Delete featured image if exists
        if ($page->featured_image) {
            Storage::disk('public')->delete($page->featured_image);
        }

        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }

    /**
     * Toggle page status
     */
    public function toggleStatus($id)
    {
        $page = Page::findOrFail($id);
        $newStatus = $page->status === 'published' ? 'draft' : 'published';

        $page->update([
            'status' => $newStatus,
            'published_at' => $newStatus === 'published' ? now() : null,
        ]);

        return back()->with('success', 'Page status updated successfully.');
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured($id)
    {
        $page = Page::findOrFail($id);
        $page->update(['is_featured' => !$page->is_featured]);

        return back()->with('success', 'Page featured status updated successfully.');
    }

    /**
     * Reorder pages
     */
    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'orderedIds' => 'required|array',
            'orderedIds.*' => 'integer|exists:pages,id',
        ]);

        foreach ($validated['orderedIds'] as $index => $id) {
            Page::where('id', $id)->update(['order' => $index + 1]);
        }

        return back()->with('success', 'Pages reordered successfully.');
    }
}
