<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SmtpConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Carbon\Carbon;

class SmtpConfigurationController extends Controller
{
    /**
     * Display the SMTP configuration page
     */
    public function index()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();

        return Inertia::render('admin/smtp-config', [
            'smtpConfig' => [
                'id' => $smtpConfig->id,
                'enabled' => $smtpConfig->enabled,
                'mailer' => $smtpConfig->mailer,
                'host' => $smtpConfig->host,
                'port' => $smtpConfig->port,
                'username' => $smtpConfig->username,
                'password' => $smtpConfig->password ? '••••••••' : '', // Mask password for security
                'encryption' => $smtpConfig->encryption,
                'from_address' => $smtpConfig->from_address,
                'from_name' => $smtpConfig->from_name,
                'admin_email' => $smtpConfig->admin_email,
                'admin_notifications' => $smtpConfig->admin_notifications,
                'test_email_subject' => $smtpConfig->test_email_subject,
                'test_email_body' => $smtpConfig->test_email_body,
                'last_tested_at' => $smtpConfig->last_tested_at?->format('M d, Y H:i:s'),
                'last_test_successful' => $smtpConfig->last_test_successful,
                'last_test_error' => $smtpConfig->last_test_error,
            ],
            'encryptionOptions' => SmtpConfiguration::getEncryptionOptions(),
            'providerPresets' => SmtpConfiguration::getProviderPresets(),
        ]);
    }

    /**
     * Update SMTP configuration
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'enabled' => 'required|boolean',
            'mailer' => 'required|string|in:smtp',
            'host' => 'required_if:enabled,true|string|max:255',
            'port' => 'required_if:enabled,true|integer|min:1|max:65535',
            'username' => 'required_if:enabled,true|string|max:255',
            'password' => 'nullable|string|max:255',
            'encryption' => 'required|string|in:tls,ssl,none',
            'from_address' => 'required_if:enabled,true|email|max:255',
            'from_name' => 'required_if:enabled,true|string|max:255',
            'admin_email' => 'required|email|max:255',
            'admin_notifications' => 'required|boolean',
            'test_email_subject' => 'required|string|max:255',
            'test_email_body' => 'required|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $data = $validator->validated();

        // Only update password if a new one is provided
        if (empty($data['password']) || $data['password'] === '••••••••') {
            unset($data['password']);
        }

        $smtpConfig->update($data);

        return back()->with('success', 'SMTP configuration updated successfully');
    }

    /**
     * Test SMTP configuration
     */
    public function test(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email address provided for testing.',
                'errors' => $validator->errors(),
            ], 422);
        }

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $testEmail = $request->input('test_email');

        try {
            // Apply SMTP configuration temporarily
            $smtpConfig->applyToMailConfig();

            // Send test email
            Mail::raw($smtpConfig->test_email_body, function ($message) use ($smtpConfig, $testEmail) {
                $message->to($testEmail)
                        ->subject($smtpConfig->test_email_subject)
                        ->from($smtpConfig->from_address, $smtpConfig->from_name);
            });

            // Update test status
            $smtpConfig->update([
                'last_tested_at' => Carbon::now(),
                'last_test_successful' => true,
                'last_test_error' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully! Please check your inbox.',
            ]);

        } catch (\Exception $e) {
            // Update test status with error
            $smtpConfig->update([
                'last_tested_at' => Carbon::now(),
                'last_test_successful' => false,
                'last_test_error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get provider preset configuration
     */
    public function getProviderPreset(Request $request)
    {
        $provider = $request->input('provider');
        $presets = SmtpConfiguration::getProviderPresets();

        if (!isset($presets[$provider])) {
            return response()->json([
                'success' => false,
                'message' => 'Provider preset not found.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'preset' => $presets[$provider],
        ]);
    }
}
