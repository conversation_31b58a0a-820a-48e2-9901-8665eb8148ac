<?php

namespace App\Http\Controllers;

use App\Models\ContactManagement;
use App\Models\ContactMessage;
use App\Services\EmailNotificationService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class ContactController extends Controller
{
    protected $emailService;

    public function __construct(EmailNotificationService $emailService)
    {
        $this->emailService = $emailService;
    }
    /**
     * Display the contact management page with both tabs
     */
    public function index()
    {
        $contactManagement = ContactManagement::getOrCreate();

        $messages = ContactMessage::latest()
            ->select(['id', 'name', 'email', 'subject', 'created_at', 'read_at', 'replied_at', 'archived_at', 'status'])
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'from' => $message->name,
                    'email' => $message->email,
                    'subject' => $message->subject,
                    'date' => $message->created_at->format('M d, Y'),
                    'status' => $message->status,
                ];
            });

        return Inertia::render('admin/contact', [
            'contactManagement' => $contactManagement,
            'messages' => $messages,
            'counts' => [
                'all' => ContactMessage::count(),
                'unread' => ContactMessage::unread()->count(),
                'read' => ContactMessage::read()->count(),
                'replied' => ContactMessage::replied()->count(),
                'archived' => ContactMessage::archived()->count(),
            ],
        ]);
    }

    /**
     * Update contact management settings
     */
    public function updateManagement(Request $request)
    {
        $validated = $request->validate([
            'section_badge' => 'required|string|max:255',
            'section_title' => 'required|string|max:255',
            'section_description' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'form_name_label' => 'required|string|max:255',
            'form_name_placeholder' => 'required|string|max:255',
            'form_email_label' => 'required|string|max:255',
            'form_email_placeholder' => 'required|string|max:255',
            'form_subject_label' => 'required|string|max:255',
            'form_subject_placeholder' => 'required|string|max:255',
            'form_message_label' => 'required|string|max:255',
            'form_message_placeholder' => 'required|string|max:255',
            'form_submit_button_text' => 'required|string|max:255',
            'email_label' => 'required|string|max:255',
            'email_subtitle' => 'required|string|max:255',
            'phone_label' => 'required|string|max:255',
            'phone_subtitle' => 'required|string|max:255',
            'location_label' => 'required|string|max:255',
            'location_subtitle' => 'required|string|max:255',
            'success_message' => 'required|string',
            'error_message' => 'required|string',
        ]);

        $contactManagement = ContactManagement::getOrCreate();
        $contactManagement->update($validated);

        return back()->with('success', 'Contact settings updated successfully');
    }

    /**
     * Store a new contact message from the public form
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Create the contact message
        $contactMessage = ContactMessage::create($validated);

        // Send email notifications
        try {
            // Send notification to admin
            $this->emailService->sendContactNotification($contactMessage);

            // Send auto-reply to user (optional)
            $this->emailService->sendContactAutoReply($contactMessage);
        } catch (\Exception $e) {
            // Log the error but don't fail the contact form submission
            \Log::error('Failed to send contact email notifications', [
                'contact_message_id' => $contactMessage->id,
                'error' => $e->getMessage(),
            ]);
        }

        $contactManagement = ContactManagement::getOrCreate();

        return back()->with('success', $contactManagement->success_message);
    }

    /**
     * Show a specific message
     */
    public function show(ContactMessage $message)
    {
        if ($message->status === 'unread') {
            $message->update([
                'status' => 'read',
                'read_at' => Carbon::now()
            ]);
        }

        return response()->json([
            'message' => $message
        ]);
    }

    /**
     * Mark message as read
     */
    public function markAsRead(ContactMessage $message)
    {
        $message->update([
            'status' => 'read',
            'read_at' => Carbon::now()
        ]);

        return back();
    }

    /**
     * Mark message as unread
     */
    public function markAsUnread(ContactMessage $message)
    {
        $message->update([
            'status' => 'unread',
            'read_at' => null
        ]);

        return back();
    }

    /**
     * Reply to a message
     */
    public function reply(ContactMessage $message, Request $request)
    {
        $request->validate([
            'reply' => 'required|string'
        ]);

        // Here you would typically send the email reply
        // For now, we'll just mark it as replied
        $message->update([
            'status' => 'replied',
            'replied_at' => Carbon::now()
        ]);

        return back();
    }

    /**
     * Archive a message
     */
    public function archive(ContactMessage $message)
    {
        $message->update([
            'status' => 'archived',
            'archived_at' => Carbon::now()
        ]);

        return back();
    }

    /**
     * Unarchive a message
     */
    public function unarchive(ContactMessage $message)
    {
        $message->update([
            'status' => 'read',
            'archived_at' => null
        ]);

        return back();
    }

    /**
     * Delete a message
     */
    public function destroy(ContactMessage $message)
    {
        $message->delete();

        return back();
    }
}
