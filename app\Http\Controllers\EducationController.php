<?php

namespace App\Http\Controllers;

use App\Models\Education;
use App\Models\ResumeContent;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EducationController extends Controller
{
    public function index()
    {
        $education = Education::orderBy('order')->get();
        return response()->json($education);
    }

    public function store(Request $request)
    {
        // Check if adding this education would exceed the display limit
        $resumeContent = ResumeContent::getOrCreate();
        $currentCount = Education::count();

        if ($currentCount >= $resumeContent->education_display_limit) {
            return response()->json([
                'error' => 'Cannot add more education entries. Maximum limit of ' . $resumeContent->education_display_limit . ' reached.',
                'limit' => $resumeContent->education_display_limit,
                'current_count' => $currentCount
            ], 422);
        }

        $validated = $request->validate([
            'degree' => 'required|string|max:255',
            'institution' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'start_date' => 'required|string',
            'end_date' => 'nullable|string',
            'description' => 'nullable|string',
        ]);

        $maxOrder = Education::max('order') ?? 0;
        $validated['order'] = $maxOrder + 1;

        $education = Education::create($validated);
        return response()->json($education, 201);
    }

    public function update(Request $request, Education $education)
    {
        $validated = $request->validate([
            'degree' => 'required|string|max:255',
            'institution' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'start_date' => 'required|string',
            'end_date' => 'nullable|string',
            'description' => 'nullable|string',
        ]);

        $education->update($validated);
        return response()->json($education);
    }

    public function destroy(Education $education)
    {
        $education->delete();
        return response()->json(null, 204);
    }

    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'education' => 'required|array',
            'education.*.id' => 'required|exists:education,id',
            'education.*.order' => 'required|integer|min:0',
        ]);

        foreach ($validated['education'] as $item) {
            Education::where('id', $item['id'])->update(['order' => $item['order']]);
        }

        return response()->json(['message' => 'Education entries reordered successfully']);
    }

    public function toggleVisibility(Education $education)
    {
        $education->update(['is_visible' => !$education->is_visible]);

        $status = $education->is_visible ? 'visible' : 'hidden';
        return response()->json([
            'message' => "Education '{$education->degree}' is now {$status}",
            'is_visible' => $education->is_visible
        ]);
    }

    public function batchUpdate(Request $request)
    {
        $validated = $request->validate([
            'education' => 'required|array',
            'education.*.id' => 'required|exists:education,id',
            'education.*.order' => 'sometimes|integer|min:0',
            'education.*.is_visible' => 'sometimes|boolean',
        ]);

        foreach ($validated['education'] as $item) {
            $updateData = [];
            if (isset($item['order'])) {
                $updateData['order'] = $item['order'];
            }
            if (isset($item['is_visible'])) {
                $updateData['is_visible'] = $item['is_visible'];
            }

            if (!empty($updateData)) {
                Education::where('id', $item['id'])->update($updateData);
            }
        }

        return response()->json(['message' => 'Education entries updated successfully']);
    }
}
