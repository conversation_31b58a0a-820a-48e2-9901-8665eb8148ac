<?php

namespace App\Http\Controllers;

use App\Models\Experience;
use App\Models\ResumeContent;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExperienceController extends Controller
{
    public function index()
    {
        $experiences = Experience::orderBy('order')->get();
        return response()->json($experiences);
    }

    public function store(Request $request)
    {
        // Check if adding this experience would exceed the display limit
        $resumeContent = ResumeContent::getOrCreate();
        $currentCount = Experience::count();

        if ($currentCount >= $resumeContent->experience_display_limit) {
            return response()->json([
                'error' => 'Cannot add more experiences. Maximum limit of ' . $resumeContent->experience_display_limit . ' reached.',
                'limit' => $resumeContent->experience_display_limit,
                'current_count' => $currentCount
            ], 422);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'start_date' => 'required|string',
            'end_date' => 'nullable|string',
            'is_current' => 'required|boolean',
            'description' => 'required|string',
        ]);

        $maxOrder = Experience::max('order') ?? 0;
        $validated['order'] = $maxOrder + 1;
        $validated['type'] = 'work'; // Default type

        $experience = Experience::create($validated);
        return response()->json($experience, 201);
    }

    public function update(Request $request, Experience $experience)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'start_date' => 'required|string',
            'end_date' => 'nullable|string',
            'is_current' => 'required|boolean',
            'description' => 'required|string',
        ]);

        $experience->update($validated);
        return response()->json($experience);
    }

    public function destroy(Experience $experience)
    {
        $experience->delete();
        return response()->json(null, 204);
    }

    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'experiences' => 'required|array',
            'experiences.*.id' => 'required|exists:experiences,id',
            'experiences.*.order' => 'required|integer|min:0',
        ]);

        foreach ($validated['experiences'] as $item) {
            Experience::where('id', $item['id'])->update(['order' => $item['order']]);
        }

        return response()->json(['message' => 'Experiences reordered successfully']);
    }

    public function toggleVisibility(Experience $experience)
    {
        $experience->update(['is_visible' => !$experience->is_visible]);

        $status = $experience->is_visible ? 'visible' : 'hidden';
        return response()->json([
            'message' => "Experience '{$experience->title}' is now {$status}",
            'is_visible' => $experience->is_visible
        ]);
    }

    public function batchUpdate(Request $request)
    {
        $validated = $request->validate([
            'experiences' => 'required|array',
            'experiences.*.id' => 'required|exists:experiences,id',
            'experiences.*.order' => 'sometimes|integer|min:0',
            'experiences.*.is_visible' => 'sometimes|boolean',
        ]);

        foreach ($validated['experiences'] as $item) {
            $updateData = [];
            if (isset($item['order'])) {
                $updateData['order'] = $item['order'];
            }
            if (isset($item['is_visible'])) {
                $updateData['is_visible'] = $item['is_visible'];
            }

            if (!empty($updateData)) {
                Experience::where('id', $item['id'])->update($updateData);
            }
        }

        return response()->json(['message' => 'Experiences updated successfully']);
    }
}
