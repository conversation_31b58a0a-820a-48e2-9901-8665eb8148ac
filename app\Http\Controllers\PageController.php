<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\HeroSection;
use App\Models\Service;
use App\Models\Project;
use App\Models\Testimonial;
use App\Models\Skill;
use App\Models\Experience;
use App\Models\Education;
use App\Models\ResumeContent;
use App\Models\TestimonialsContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PageController extends Controller
{
    /**
     * Display the specified page
     */
    public function show($slug)
    {
        $page = Page::where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Get all data needed for the main layout (same as WelcomeController)
        $heroSection = HeroSection::getOrCreate();

        // Get services data
        $services = Service::where('is_active', true)
            ->orderBy('order')
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'title' => $service->title,
                    'description' => $service->description,
                    'icon' => $service->icon,
                    'order' => $service->order,
                ];
            });

        // Get projects data
        $projects = Project::where('status', 'published')
            ->orderBy('order')
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'image' => $project->image ? Storage::url($project->image) : null,
                    'technologies' => $project->technologies,
                    'category' => $project->category,
                    'project_url' => $project->project_url,
                    'github_url' => $project->github_url,
                    'order' => $project->order,
                ];
            });

        // Get testimonials data (no visibility filter needed)
        $testimonials = Testimonial::orderBy('order')
            ->get()
            ->map(function ($testimonial) {
                return [
                    'id' => $testimonial->id,
                    'name' => $testimonial->name,
                    'position' => $testimonial->position,
                    'company' => $testimonial->company,
                    'content' => $testimonial->quote, // Note: using 'quote' field from model
                    'avatar' => $testimonial->image ? Storage::url($testimonial->image) : null, // Note: using 'image' field from model
                    'rating' => $testimonial->rating,
                    'order' => $testimonial->order,
                ];
            });

        // Get skills data
        $skills = Skill::where('is_visible', true)
            ->orderBy('order')
            ->get()
            ->map(function ($skill) {
                return [
                    'id' => $skill->id,
                    'name' => $skill->name,
                    'level' => $skill->level,
                    'category' => $skill->category,
                    'order' => $skill->order,
                ];
            });

        // Get experiences data
        $experiences = Experience::where('is_visible', true)
            ->orderBy('order')
            ->get()
            ->map(function ($experience) {
                return [
                    'id' => $experience->id,
                    'title' => $experience->title,
                    'company' => $experience->company,
                    'location' => $experience->location,
                    'start_date' => $experience->start_date,
                    'end_date' => $experience->end_date,
                    'is_current' => $experience->is_current,
                    'description' => $experience->description,
                    'order' => $experience->order,
                ];
            });

        // Get education data
        $education = Education::where('is_visible', true)
            ->orderBy('order')
            ->get()
            ->map(function ($edu) {
                return [
                    'id' => $edu->id,
                    'degree' => $edu->degree,
                    'institution' => $edu->institution,
                    'location' => $edu->location,
                    'start_date' => $edu->start_date,
                    'end_date' => $edu->end_date,
                    'is_current' => $edu->is_current,
                    'description' => $edu->description,
                    'order' => $edu->order,
                ];
            });

        // Get resume content
        $resumeContent = ResumeContent::first();

        // Get testimonials content
        $testimonialsContent = TestimonialsContent::first();

        return Inertia::render('page-with-home-layout', [
            'page' => [
                'id' => $page->id,
                'title' => $page->title,
                'slug' => $page->slug,
                'content' => $page->content,
                'meta_description' => $page->meta_description,
                'meta_keywords' => $page->meta_keywords,
                'featured_image' => $page->featured_image ? Storage::url($page->featured_image) : null,
                'page_layout' => $page->page_layout,
                'published_at' => $page->published_at?->format('M d, Y'),
                'is_featured' => $page->is_featured,
            ],
            'profile' => [
                'page_title' => $heroSection->page_title,
                'title' => $heroSection->title,
                'about' => $heroSection->about,
                'years_experience' => $heroSection->years_experience,
                'projects_completed' => $heroSection->projects_completed,
                'is_available' => $heroSection->is_available,
                'cta_text' => $heroSection->cta_text,
                'cta_secondary_text' => $heroSection->cta_secondary_text,
                'cta_url' => $heroSection->cta_url,
                'cta_secondary_url' => $heroSection->cta_secondary_url,
                'avatar' => $heroSection->avatar ? Storage::url($heroSection->avatar) : null,
                'logo' => [
                    'text' => $heroSection->logo_text,
                    'type' => $heroSection->logo_type,
                    'icon' => $heroSection->logo_icon,
                    'icon_type' => $heroSection->logo_icon && str_starts_with($heroSection->logo_icon, '<svg') ? 'svg' : 'letter',
                    'color' => $heroSection->logo_color,
                ],
                'navbar_items' => $heroSection->navbar_items ?: [
                    ['title' => 'Home', 'href' => 'home'],
                    ['title' => 'Services', 'href' => 'services'],
                    ['title' => 'Works', 'href' => 'works'],
                    ['title' => 'Skills', 'href' => 'skills'],
                    ['title' => 'Resume', 'href' => 'resume'],
                    ['title' => 'Testimonials', 'href' => 'testimonials'],
                    ['title' => 'Contact', 'href' => 'contact']
                ],
                'hire_me' => [
                    'text' => $heroSection->hire_me_text,
                    'url' => $heroSection->hire_me_url,
                    'enabled' => $heroSection->hire_me_enabled,
                ],
                'social_links' => $heroSection->social_links ?: [],
            ],
            'services' => $services,
            'projects' => $projects,
            'testimonials' => $testimonials,
            'skills' => $skills,
            'experiences' => $experiences,
            'education' => $education,
            'resumeContent' => $resumeContent ? [
                'section_badge' => $resumeContent->section_badge,
                'section_title' => $resumeContent->section_title,
                'section_description' => $resumeContent->section_description,
                'experience_title' => $resumeContent->experience_title,
                'education_title' => $resumeContent->education_title,
                'download_button_text' => $resumeContent->download_button_text,
                'resume_file_path' => $resumeContent->resume_file_path,
            ] : [
                'section_badge' => 'Resume',
                'section_title' => 'Experience & Education',
                'section_description' => 'My professional journey and academic background',
                'experience_title' => 'Experience',
                'education_title' => 'Education',
                'download_button_text' => 'Download Resume',
                'resume_file_path' => null,
            ],
            'testimonialsContent' => $testimonialsContent ? [
                'section_badge' => $testimonialsContent->section_badge,
                'section_title' => $testimonialsContent->section_title,
                'section_description' => $testimonialsContent->section_description,
            ] : [
                'section_badge' => 'Testimonials',
                'section_title' => 'What Clients Say',
                'section_description' => 'Feedback from satisfied clients and collaborators',
            ],
        ]);
    }

    /**
     * Display a listing of published pages
     */
    public function index()
    {
        $pages = Page::published()
            ->orderBy('order')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($page) {
                return [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'excerpt' => $page->excerpt,
                    'featured_image' => $page->featured_image ? Storage::url($page->featured_image) : null,
                    'published_at' => $page->published_at?->format('M d, Y'),
                    'is_featured' => $page->is_featured,
                    'url' => $page->url,
                ];
            });

        // Get profile data for layout
        $profile = HeroSection::getOrCreate();

        return Inertia::render('pages', [
            'pages' => $pages,
            'profile' => [
                'page_title' => $profile->page_title,
                'logo_text' => $profile->logo_text,
                'logo_type' => $profile->logo_type,
                'logo_icon' => $profile->logo_icon,
                'logo_color' => $profile->logo_color,
                'navbar_items' => $profile->navbar_items,
                'hire_me_enabled' => $profile->hire_me_enabled,
                'hire_me_text' => $profile->hire_me_text,
                'hire_me_url' => $profile->hire_me_url,
            ],
        ]);
    }
}
