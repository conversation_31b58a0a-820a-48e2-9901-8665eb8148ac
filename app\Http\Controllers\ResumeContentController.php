<?php

namespace App\Http\Controllers;

use App\Models\ResumeContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ResumeContentController extends Controller
{
    public function index()
    {
        $resumeContent = ResumeContent::getOrCreate();
        return response()->json($resumeContent);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'section_badge' => 'required|string|max:255',
            'section_title' => 'required|string|max:255',
            'section_description' => 'required|string',
            'experience_title' => 'required|string|max:255',
            'education_title' => 'required|string|max:255',
            'download_button_text' => 'required|string|max:255',
            'experience_display_limit' => 'required|integer|min:1|max:10',
            'education_display_limit' => 'required|integer|min:1|max:10',
        ]);

        $resumeContent = ResumeContent::getOrCreate();
        $resumeContent->update($validated);

        return response()->json($resumeContent);
    }

    public function uploadResume(Request $request)
    {
        $request->validate([
            'resume' => 'required|file|mimes:pdf|max:10240', // 10MB max
        ]);

        $resumeContent = ResumeContent::getOrCreate();

        // Delete old resume file if exists
        if ($resumeContent->resume_file_path && Storage::disk('public')->exists($resumeContent->resume_file_path)) {
            Storage::disk('public')->delete($resumeContent->resume_file_path);
        }

        // Store new resume file
        $path = $request->file('resume')->store('resumes', 'public');
        
        $resumeContent->update([
            'resume_file_path' => $path
        ]);

        return response()->json([
            'message' => 'Resume uploaded successfully',
            'file_path' => $path
        ]);
    }

    public function downloadResume()
    {
        $resumeContent = ResumeContent::getOrCreate();
        
        if (!$resumeContent->resume_file_path || !Storage::disk('public')->exists($resumeContent->resume_file_path)) {
            return response()->json(['error' => 'Resume file not found'], 404);
        }

        return Storage::disk('public')->download($resumeContent->resume_file_path, 'resume.pdf');
    }

    public function deleteResume()
    {
        $resumeContent = ResumeContent::getOrCreate();
        
        if ($resumeContent->resume_file_path && Storage::disk('public')->exists($resumeContent->resume_file_path)) {
            Storage::disk('public')->delete($resumeContent->resume_file_path);
        }

        $resumeContent->update([
            'resume_file_path' => null
        ]);

        return response()->json(['message' => 'Resume deleted successfully']);
    }
}
