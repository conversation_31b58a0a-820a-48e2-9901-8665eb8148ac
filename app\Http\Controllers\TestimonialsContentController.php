<?php

namespace App\Http\Controllers;

use App\Models\TestimonialsContent;
use Illuminate\Http\Request;

class TestimonialsContentController extends Controller
{
    public function index()
    {
        $testimonialsContent = TestimonialsContent::getOrCreate();
        return response()->json($testimonialsContent);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'section_badge' => 'required|string|max:255',
            'section_title' => 'required|string|max:255',
            'section_description' => 'required|string',
        ]);

        $testimonialsContent = TestimonialsContent::getOrCreate();
        $testimonialsContent->update($validated);

        return response()->json($testimonialsContent);
    }
}
