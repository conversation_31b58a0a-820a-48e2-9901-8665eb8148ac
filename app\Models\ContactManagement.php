<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ContactManagement extends Model
{
    protected $table = 'contact_management';

    protected $fillable = [
        'section_badge',
        'section_title',
        'section_description',
        'email',
        'phone',
        'location',
        'form_name_label',
        'form_name_placeholder',
        'form_email_label',
        'form_email_placeholder',
        'form_subject_label',
        'form_subject_placeholder',
        'form_message_label',
        'form_message_placeholder',
        'form_submit_button_text',
        'email_label',
        'email_subtitle',
        'phone_label',
        'phone_subtitle',
        'location_label',
        'location_subtitle',
        'success_message',
        'error_message',
    ];

    /**
     * Get the first (and should be only) contact management record.
     * Create one with defaults if it doesn't exist.
     *
     * @return ContactManagement
     */
    public static function getOrCreate()
    {
        $record = static::first();

        if (!$record) {
            $record = static::create([
                'section_badge' => 'Contact',
                'section_title' => 'Get In Touch',
                'section_description' => 'Have a project in mind? Let us discuss your ideas and bring them to life.',
                'email' => '<EMAIL>',
                'phone' => '+880 (781) 935014',
                'location' => 'Kushtia, Bangladesh',
                'form_name_label' => 'Name',
                'form_name_placeholder' => 'Your Name',
                'form_email_label' => 'Email',
                'form_email_placeholder' => '<EMAIL>',
                'form_subject_label' => 'Subject',
                'form_subject_placeholder' => 'Project Subject',
                'form_message_label' => 'Message',
                'form_message_placeholder' => 'Your Message',
                'form_submit_button_text' => 'Send Message',
                'email_label' => 'Email',
                'email_subtitle' => 'For general inquiries:',
                'phone_label' => 'Phone',
                'phone_subtitle' => 'Available Monday-Friday:',
                'location_label' => 'Location',
                'location_subtitle' => 'Based in:',
                'success_message' => 'Your message has been sent successfully. I will get back to you soon!',
                'error_message' => 'Sorry, there was an error sending your message. Please try again.',
            ]);
        }

        return $record;
    }
}
