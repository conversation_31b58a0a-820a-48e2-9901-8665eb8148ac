<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Education extends Model
{
    protected $fillable = [
        'degree',
        'institution',
        'location',
        'start_date',
        'end_date',
        'description',
        'order',
        'is_visible',
    ];

    protected $casts = [
        'is_visible' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the formatted start date.
     */
    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date?->format('M Y') ?? '';
    }

    /**
     * Get the formatted end date.
     */
    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date?->format('M Y') ?? '';
    }

    /**
     * Get the formatted period string.
     */
    public function getFormattedPeriodAttribute(): string
    {
        $startDate = $this->formatted_start_date;
        $endDate = $this->end_date ? $this->formatted_end_date : 'Present';

        return $startDate . ' - ' . $endDate;
    }
}
