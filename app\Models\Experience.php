<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Experience extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'company',
        'location',
        'start_date',
        'end_date',
        'is_current',
        'description',
        'type',
        'order',
        'is_visible',
    ];

    protected $casts = [
        'is_current' => 'boolean',
        'is_visible' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the formatted start date.
     */
    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date?->format('M Y') ?? '';
    }

    /**
     * Get the formatted end date.
     */
    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date?->format('M Y') ?? '';
    }

    /**
     * Get the formatted period string.
     */
    public function getFormattedPeriodAttribute(): string
    {
        $startDate = $this->formatted_start_date;
        $endDate = $this->is_current ? 'Present' : $this->formatted_end_date;

        return $startDate . ' - ' . $endDate;
    }
}
