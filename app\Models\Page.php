<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Page extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'meta_description',
        'meta_keywords',
        'featured_image',
        'page_layout',
        'status',
        'publish_immediately',
        'published_at',
        'order',
        'is_featured',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'publish_immediately' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }

            // Auto-publish if publish_immediately is true
            if ($page->publish_immediately) {
                $page->status = 'published';
                $page->published_at = now();
            }
        });

        static::updating(function ($page) {
            // Auto-publish if publish_immediately is true
            if ($page->publish_immediately && $page->status !== 'published') {
                $page->status = 'published';
                $page->published_at = now();
            }
        });
    }

    /**
     * Scope for published pages
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->where(function ($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '<=', now());
                    });
    }

    /**
     * Scope for draft pages
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope for featured pages
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the route key for the model
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the URL for the page
     */
    public function getUrlAttribute()
    {
        return route('pages.show', $this->slug);
    }

    /**
     * Get excerpt from content
     */
    public function getExcerptAttribute($length = 150)
    {
        return Str::limit(strip_tags($this->content), $length);
    }

    /**
     * Check if page is published
     */
    public function getIsPublishedAttribute()
    {
        return $this->status === 'published' &&
               ($this->published_at === null || $this->published_at <= now());
    }
}
