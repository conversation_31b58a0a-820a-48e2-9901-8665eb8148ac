<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResumeContent extends Model
{
    protected $table = 'resume_content';
    
    protected $fillable = [
        'section_badge',
        'section_title',
        'section_description',
        'experience_title',
        'education_title',
        'download_button_text',
        'resume_file_path',
        'experience_display_limit',
        'education_display_limit',
    ];

    /**
     * Get or create the resume content instance
     */
    public static function getOrCreate()
    {
        return static::firstOrCreate(
            ['id' => 1],
            [
                'section_badge' => 'Resume',
                'section_title' => 'Experience & Education',
                'section_description' => 'My professional journey and academic background',
                'experience_title' => 'Experience',
                'education_title' => 'Education',
                'download_button_text' => 'Download Full Resume',
                'resume_file_path' => null,
                'experience_display_limit' => 3,
                'education_display_limit' => 3,
            ]
        );
    }
}
