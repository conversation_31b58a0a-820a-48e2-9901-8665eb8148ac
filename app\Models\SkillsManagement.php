<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SkillsManagement extends Model
{
    protected $table = 'skills_management';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        // Home page skills section content
        'skills_section_badge',
        'skills_section_title',
        'skills_section_description',
    ];

    /**
     * Get or create the skills management record.
     * There should only be one record for content management.
     *
     * @return SkillsManagement
     */
    public static function getOrCreate()
    {
        $record = static::first();
        
        if (!$record) {
            $record = static::create([
                'skills_section_badge' => 'Skills',
                'skills_section_title' => 'Technical Proficiency',
                'skills_section_description' => 'I have spent years honing my skills in various technologies and design principles',
            ]);
        }
        
        return $record;
    }
}
