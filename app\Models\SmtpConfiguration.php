<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;

class SmtpConfiguration extends Model
{
    use HasFactory;

    protected $table = 'smtp_configurations';

    protected $fillable = [
        'enabled',
        'mailer',
        'host',
        'port',
        'username',
        'password',
        'encryption',
        'from_address',
        'from_name',
        'admin_email',
        'admin_notifications',
        'test_email_subject',
        'test_email_body',
        'last_tested_at',
        'last_test_successful',
        'last_test_error',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'admin_notifications' => 'boolean',
        'port' => 'integer',
        'last_tested_at' => 'datetime',
        'last_test_successful' => 'boolean',
    ];

    protected $hidden = [
        'password',
    ];

    /**
     * Get the first (and should be only) SMTP configuration record.
     * Create one with defaults if it doesn't exist.
     *
     * @return SmtpConfiguration
     */
    public static function getOrCreate()
    {
        $record = static::first();

        if (!$record) {
            $record = static::create([
                'enabled' => false,
                'mailer' => 'smtp',
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'username' => '',
                'password' => '',
                'encryption' => 'tls',
                'from_address' => '<EMAIL>',
                'from_name' => 'Portfolio Contact',
                'admin_email' => '<EMAIL>',
                'admin_notifications' => true,
                'test_email_subject' => 'Test Email from Portfolio',
                'test_email_body' => 'This is a test email to verify SMTP configuration is working correctly.',
                'last_test_successful' => false,
            ]);
        }

        return $record;
    }

    /**
     * Encrypt password before saving
     */
    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = Crypt::encryptString($value);
        }
    }

    /**
     * Decrypt password when retrieving
     */
    public function getPasswordAttribute($value)
    {
        if ($value) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Apply SMTP configuration to Laravel mail config
     */
    public function applyToMailConfig()
    {
        if (!$this->enabled) {
            return false;
        }

        Config::set([
            'mail.default' => $this->mailer,
            'mail.mailers.smtp.host' => $this->host,
            'mail.mailers.smtp.port' => $this->port,
            'mail.mailers.smtp.username' => $this->username,
            'mail.mailers.smtp.password' => $this->password,
            'mail.mailers.smtp.encryption' => $this->encryption === 'none' ? null : $this->encryption,
            'mail.from.address' => $this->from_address,
            'mail.from.name' => $this->from_name,
        ]);

        return true;
    }

    /**
     * Get default SMTP configuration data for fallback
     *
     * @return array
     */
    public static function getDefaults()
    {
        return [
            'enabled' => false,
            'mailer' => 'smtp',
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '',
            'password' => '',
            'encryption' => 'tls',
            'from_address' => '<EMAIL>',
            'from_name' => 'Portfolio Contact',
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
            'test_email_subject' => 'Test Email from Portfolio',
            'test_email_body' => 'This is a test email to verify SMTP configuration is working correctly.',
            'last_test_successful' => false,
        ];
    }

    /**
     * Get encryption options
     */
    public static function getEncryptionOptions()
    {
        return [
            'tls' => 'TLS',
            'ssl' => 'SSL',
            'none' => 'None',
        ];
    }

    /**
     * Get common SMTP providers with their default settings
     */
    public static function getProviderPresets()
    {
        return [
            'gmail' => [
                'name' => 'Gmail',
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'encryption' => 'tls',
            ],
            'outlook' => [
                'name' => 'Outlook/Hotmail',
                'host' => 'smtp-mail.outlook.com',
                'port' => 587,
                'encryption' => 'tls',
            ],
            'yahoo' => [
                'name' => 'Yahoo Mail',
                'host' => 'smtp.mail.yahoo.com',
                'port' => 587,
                'encryption' => 'tls',
            ],
            'sendgrid' => [
                'name' => 'SendGrid',
                'host' => 'smtp.sendgrid.net',
                'port' => 587,
                'encryption' => 'tls',
            ],
            'mailgun' => [
                'name' => 'Mailgun',
                'host' => 'smtp.mailgun.org',
                'port' => 587,
                'encryption' => 'tls',
            ],
            'custom' => [
                'name' => 'Custom SMTP',
                'host' => '',
                'port' => 587,
                'encryption' => 'tls',
            ],
        ];
    }
}
