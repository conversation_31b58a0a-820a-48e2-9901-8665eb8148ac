<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TestimonialsContent extends Model
{
    protected $table = 'testimonials_content';

    protected $fillable = [
        'section_badge',
        'section_title',
        'section_description',
    ];

    /**
     * Get or create the testimonials content instance
     */
    public static function getOrCreate()
    {
        return static::firstOrCreate(
            ['id' => 1],
            [
                'section_badge' => 'Testimonials',
                'section_title' => 'What Clients Say',
                'section_description' => 'Feedback from clients who have experienced working with me.',
            ]
        );
    }
}
