<?php

namespace App\Services;

use App\Models\SmtpConfiguration;
use App\Models\ContactMessage;
use App\Mail\ContactNotification;
use App\Mail\ContactAutoReply;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailNotificationService
{
    /**
     * Send contact form notification to admin
     */
    public function sendContactNotification(ContactMessage $contactMessage)
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();

        // Check if SMTP is enabled and admin notifications are enabled
        if (!$smtpConfig->enabled || !$smtpConfig->admin_notifications || !$smtpConfig->admin_email) {
            Log::info('Contact notification not sent: SMTP disabled or admin email not configured');
            return false;
        }

        try {
            // Apply SMTP configuration
            $smtpConfig->applyToMailConfig();

            // Send notification email to admin
            Mail::to($smtpConfig->admin_email)
                ->send(new ContactNotification($contactMessage, route('admin.contact')));

            Log::info('Contact notification sent successfully', [
                'contact_message_id' => $contactMessage->id,
                'admin_email' => $smtpConfig->admin_email,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send contact notification', [
                'contact_message_id' => $contactMessage->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Send auto-reply to contact form submitter
     */
    public function sendContactAutoReply(ContactMessage $contactMessage)
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();

        // Check if SMTP is enabled
        if (!$smtpConfig->enabled) {
            Log::info('Auto-reply not sent: SMTP disabled');
            return false;
        }

        try {
            // Apply SMTP configuration
            $smtpConfig->applyToMailConfig();

            // Send auto-reply email
            Mail::to($contactMessage->email, $contactMessage->name)
                ->send(new ContactAutoReply($contactMessage));

            Log::info('Auto-reply sent successfully', [
                'contact_message_id' => $contactMessage->id,
                'recipient_email' => $contactMessage->email,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send auto-reply', [
                'contact_message_id' => $contactMessage->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Send test email
     */
    public function sendTestEmail($toEmail, $subject = null, $body = null)
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();

        if (!$smtpConfig->enabled) {
            throw new \Exception('SMTP is not enabled');
        }

        // Apply SMTP configuration
        $smtpConfig->applyToMailConfig();

        $subject = $subject ?: $smtpConfig->test_email_subject;
        $body = $body ?: $smtpConfig->test_email_body;

        Mail::raw($body, function ($message) use ($smtpConfig, $toEmail, $subject) {
            $message->to($toEmail)
                    ->subject($subject)
                    ->from($smtpConfig->from_address, $smtpConfig->from_name);
        });

        return true;
    }

    /**
     * Check if email notifications are enabled
     */
    public function isEnabled()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        return $smtpConfig->enabled;
    }

    /**
     * Check if admin notifications are enabled
     */
    public function isAdminNotificationEnabled()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        return $smtpConfig->enabled && $smtpConfig->admin_notifications && $smtpConfig->admin_email;
    }
}
