-- Check and clean project categories in database
-- Run this in phpMyAdmin to see what categories exist and clean unwanted ones

-- Step 1: See all projects and their categories
SELECT 'All projects in database:' as info;
SELECT id, title, category, status, created_at 
FROM projects 
ORDER BY category, title;

-- Step 2: See unique categories in database
SELECT 'Unique categories in database:' as info;
SELECT category, COUNT(*) as project_count, 
       GROUP_CONCAT(title SEPARATOR ', ') as projects
FROM projects 
WHERE category IS NOT NULL AND category != ''
GROUP BY category 
ORDER BY category;

-- Step 3: See which categories you might want to remove
SELECT 'Categories that might need cleaning:' as info;
SELECT category, COUNT(*) as project_count
FROM projects 
WHERE category IN ('Web App', 'Branding', 'App Development', 'UI/UX Design')
GROUP BY category;

-- Step 4: If you want to remove projects with unwanted categories, uncomment these:
-- DELETE FROM projects WHERE category = 'Web App';
-- DELETE FROM projects WHERE category = 'Branding';

-- Step 5: Or if you want to update categories to match your desired ones:
-- UPDATE projects SET category = 'Web Design' WHERE category = 'Web App';
-- UPDATE projects SET category = 'Web Design' WHERE category = 'UI/UX Design';

-- Step 6: After cleaning, see the final result
SELECT 'Final categories after cleaning:' as info;
SELECT DISTINCT category 
FROM projects 
WHERE category IS NOT NULL AND category != ''
ORDER BY category;
