-- Cleanup unwanted categories from projects table
-- This will make the admin filter show only desired categories

-- Step 1: Backup - See what will be affected
SELECT 'Projects that will be affected:' as info;
SELECT id, title, category, status 
FROM projects 
WHERE category IN ('Web App', 'Branding', 'UI/UX Design', 'App Development')
ORDER BY category, title;

-- Step 2: Choose your cleanup method:

-- METHOD A: Delete projects with unwanted categories (CAREFUL - this deletes data!)
-- Uncomment the lines below if you want to DELETE these projects:
-- DELETE FROM projects WHERE category = 'Web App';
-- DELETE FROM projects WHERE category = 'Branding';
-- DELETE FROM projects WHERE category = 'UI/UX Design';
-- DELETE FROM projects WHERE category = 'App Development';

-- METHOD B: Update categories to desired ones (SAFER - keeps projects but changes categories)
-- Uncomment and modify the lines below to UPDATE categories:
-- UPDATE projects SET category = 'Web Design' WHERE category = 'Web App';
-- UPDATE projects SET category = 'Web Design' WHERE category = 'UI/UX Design';
-- UPDATE projects SET category = 'Web Design' WHERE category = 'Branding';
-- UPDATE projects SET category = 'Mobile App' WHERE category = 'App Development';

-- METHOD C: Move unwanted projects to draft status (SAFEST - keeps data but hides from public)
-- UPDATE projects SET status = 'draft' WHERE category IN ('Web App', 'Branding', 'UI/UX Design', 'App Development');

-- Step 3: After cleanup, verify the results
SELECT 'Final categories in database:' as info;
SELECT DISTINCT category, COUNT(*) as project_count
FROM projects 
WHERE category IS NOT NULL AND category != ''
GROUP BY category 
ORDER BY category;

-- Step 4: Clear projects_management filter_categories to force resync
UPDATE projects_management SET filter_categories = '[]';

-- Note: After running this, the admin panel filter will automatically show only 
-- the categories that exist in your projects table.
