-- Clear hardcoded categories and sync with actual project categories
-- Run this in phpMyAdmin to ensure categories are fully dynamic

-- Step 1: See current state
SELECT 'Current projects with categories:' as info;
SELECT id, title, category, status FROM projects 
WHERE category IS NOT NULL AND category != '' 
ORDER BY category, title;

SELECT 'Current projects_management filter_categories:' as info;
SELECT id, filter_categories FROM projects_management;

-- Step 2: Get unique categories from PUBLISHED projects only
SELECT 'Unique categories from PUBLISHED projects:' as info;
SELECT DISTINCT category FROM projects 
WHERE status = 'published' 
  AND category IS NOT NULL 
  AND category != '' 
ORDER BY category;

-- Step 3: Clear existing filter_categories to force dynamic sync
UPDATE projects_management 
SET filter_categories = '[]'
WHERE id = 1;

-- Step 4: Verify the update
SELECT 'After clearing - projects_management filter_categories:' as info;
SELECT id, filter_categories FROM projects_management;

-- Note: The system will now automatically populate filter_categories 
-- with only categories that have published projects when you visit the projects page
