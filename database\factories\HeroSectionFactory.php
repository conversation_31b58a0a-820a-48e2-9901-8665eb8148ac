<?php

namespace Database\Factories;

use App\Models\HeroSection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HeroSection>
 */
class HeroSectionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = HeroSection::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'full_name' => $this->faker->name(),
            'title' => $this->faker->jobTitle(),
            'about' => $this->faker->paragraph(),
            'years_experience' => $this->faker->numberBetween(1, 20),
            'projects_completed' => $this->faker->numberBetween(10, 500),
            'is_available' => $this->faker->boolean(),
            'cta_text' => 'Get In Touch',
            'cta_url' => '#contact',
            'cta_secondary_text' => 'View Portfolio',
            'cta_secondary_url' => '#portfolio',
            'github_url' => $this->faker->optional()->url(),
            'github_enabled' => $this->faker->boolean(),
            'linkedin_url' => $this->faker->optional()->url(),
            'linkedin_enabled' => $this->faker->boolean(),
            'twitter_url' => $this->faker->optional()->url(),
            'twitter_enabled' => $this->faker->boolean(),
            'facebook_url' => $this->faker->optional()->url(),
            'facebook_enabled' => $this->faker->boolean(),
            'instagram_url' => $this->faker->optional()->url(),
            'instagram_enabled' => $this->faker->boolean(),
            'dribbble_url' => $this->faker->optional()->url(),
            'dribbble_enabled' => $this->faker->boolean(),
            'behance_url' => $this->faker->optional()->url(),
            'behance_enabled' => $this->faker->boolean(),
            'fiverr_url' => $this->faker->optional()->url(),
            'fiverr_enabled' => $this->faker->boolean(),
            'upwork_url' => $this->faker->optional()->url(),
            'upwork_enabled' => $this->faker->boolean(),
            'freelancer_url' => $this->faker->optional()->url(),
            'freelancer_enabled' => $this->faker->boolean(),
            'peopleperhour_url' => $this->faker->optional()->url(),
            'peopleperhour_enabled' => $this->faker->boolean(),
            'upassign_url' => $this->faker->optional()->url(),
            'upassign_enabled' => $this->faker->boolean(),
            'email' => $this->faker->email(),
            'phone' => $this->faker->phoneNumber(),
            'location' => $this->faker->city() . ', ' . $this->faker->country(),
        ];
    }
}
