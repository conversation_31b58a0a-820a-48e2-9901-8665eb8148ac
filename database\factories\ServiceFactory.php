<?php

namespace Database\Factories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Service::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $icons = ['Code', 'Palette', 'Smartphone', 'Search', 'BarChart', 'FileText', 'Database', 'Shield'];
        $features = [
            'Responsive Design',
            'Modern Frameworks',
            'Performance Optimization',
            'Cross-browser Compatibility',
            'Database Integration',
            'API Development',
            'Security Implementation',
            'Testing & Quality Assurance',
            'Documentation',
            'Maintenance & Support'
        ];
        
        $technologies = [
            'React', 'Vue.js', 'Angular', 'Laravel', 'Node.js', 'PHP', 'Python', 
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Docker', 'AWS', 'Git'
        ];

        return [
            'title' => $this->faker->words(2, true),
            'description' => $this->faker->sentence(12),
            'long_description' => $this->faker->paragraph(3),
            'icon' => $this->faker->randomElement($icons),
            'price' => $this->faker->randomFloat(2, 500, 5000),
            'starting_price' => function (array $attributes) {
                return $attributes['price'];
            },
            'duration' => $this->faker->randomElement(['1-2 weeks', '2-3 weeks', '3-4 weeks', '1-2 months']),
            'projects_count' => $this->faker->numberBetween(5, 50),
            'features' => $this->faker->randomElements($features, $this->faker->numberBetween(3, 7)),
            'technologies' => $this->faker->randomElements($technologies, $this->faker->numberBetween(2, 5)),
            'image_url' => null,
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'is_featured' => $this->faker->boolean(30), // 30% chance of being featured
            'order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Indicate that the service is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the service is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the service is not featured.
     */
    public function notFeatured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => false,
        ]);
    }

    /**
     * Set a specific order for the service.
     */
    public function order(int $order): static
    {
        return $this->state(fn (array $attributes) => [
            'order' => $order,
        ]);
    }

    /**
     * Set a specific price for the service.
     */
    public function price(float $price): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price,
            'starting_price' => $price,
        ]);
    }
}
