<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('skills_management', function (Blueprint $table) {
            $table->id();
            
            // Home page skills section content
            $table->string('skills_section_badge')->default('Skills');
            $table->string('skills_section_title')->default('Technical Proficiency');
            $table->text('skills_section_description')->default('I have spent years honing my skills in various technologies and design principles');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('skills_management');
    }
};
