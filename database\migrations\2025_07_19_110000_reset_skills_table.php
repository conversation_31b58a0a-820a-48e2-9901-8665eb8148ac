<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clear all existing skills data
        DB::table('skills')->truncate();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No rollback needed for data truncation
    }
};
