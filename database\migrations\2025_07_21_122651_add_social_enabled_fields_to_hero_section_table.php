<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            $table->boolean('github_enabled')->default(true)->after('github_url');
            $table->boolean('linkedin_enabled')->default(true)->after('linkedin_url');
            $table->boolean('twitter_enabled')->default(true)->after('twitter_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            $table->dropColumn(['github_enabled', 'linkedin_enabled', 'twitter_enabled']);
        });
    }
};
