<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Facebook
            $table->string('facebook_url')->nullable()->after('twitter_enabled');
            $table->boolean('facebook_enabled')->default(true)->after('facebook_url');
            
            // Instagram
            $table->string('instagram_url')->nullable()->after('facebook_enabled');
            $table->boolean('instagram_enabled')->default(true)->after('instagram_url');
            
            // Dribbble
            $table->string('dribbble_url')->nullable()->after('instagram_enabled');
            $table->boolean('dribbble_enabled')->default(true)->after('dribbble_url');
            
            // Behance
            $table->string('behance_url')->nullable()->after('dribbble_enabled');
            $table->boolean('behance_enabled')->default(true)->after('behance_url');
            
            // Fiverr
            $table->string('fiverr_url')->nullable()->after('behance_enabled');
            $table->boolean('fiverr_enabled')->default(true)->after('fiverr_url');
            
            // Upwork
            $table->string('upwork_url')->nullable()->after('fiverr_enabled');
            $table->boolean('upwork_enabled')->default(true)->after('upwork_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            $table->dropColumn([
                'facebook_url', 'facebook_enabled',
                'instagram_url', 'instagram_enabled',
                'dribbble_url', 'dribbble_enabled',
                'behance_url', 'behance_enabled',
                'fiverr_url', 'fiverr_enabled',
                'upwork_url', 'upwork_enabled'
            ]);
        });
    }
};
