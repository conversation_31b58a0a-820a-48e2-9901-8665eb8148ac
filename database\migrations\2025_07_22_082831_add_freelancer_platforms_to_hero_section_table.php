<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Add Freelancer.com platform
            $table->string('freelancer_url')->nullable()->after('upwork_enabled');
            $table->boolean('freelancer_enabled')->default(true)->after('freelancer_url');

            // Add PeoplePerHour platform
            $table->string('peopleperhour_url')->nullable()->after('freelancer_enabled');
            $table->boolean('peopleperhour_enabled')->default(true)->after('peopleperhour_url');

            // Add Upassign platform
            $table->string('upassign_url')->nullable()->after('peopleperhour_enabled');
            $table->boolean('upassign_enabled')->default(true)->after('upassign_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Remove Upassign platform
            $table->dropColumn(['upassign_url', 'upassign_enabled']);

            // Remove PeoplePerHour platform
            $table->dropColumn(['peopleperhour_url', 'peopleperhour_enabled']);

            // Remove Freelancer.com platform
            $table->dropColumn(['freelancer_url', 'freelancer_enabled']);
        });
    }
};
