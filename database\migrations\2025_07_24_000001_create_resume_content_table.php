<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resume_content', function (Blueprint $table) {
            $table->id();
            
            // Resume section content
            $table->string('section_badge')->default('Resume');
            $table->string('section_title')->default('Experience & Education');
            $table->text('section_description')->default('My professional journey and academic background');
            
            // Experience subsection
            $table->string('experience_title')->default('Experience');
            
            // Education subsection  
            $table->string('education_title')->default('Education');
            
            // Download button
            $table->string('download_button_text')->default('Download Full Resume');
            $table->string('resume_file_path')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resume_content');
    }
};
