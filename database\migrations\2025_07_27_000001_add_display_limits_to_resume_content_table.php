<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resume_content', function (Blueprint $table) {
            // Add display limit fields for homepage
            $table->integer('experience_display_limit')->default(3)->after('resume_file_path');
            $table->integer('education_display_limit')->default(3)->after('experience_display_limit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resume_content', function (Blueprint $table) {
            $table->dropColumn(['experience_display_limit', 'education_display_limit']);
        });
    }
};
