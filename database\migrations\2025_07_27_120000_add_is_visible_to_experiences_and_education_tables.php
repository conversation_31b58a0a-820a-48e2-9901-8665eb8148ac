<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            $table->boolean('is_visible')->default(true)->after('order');
        });

        Schema::table('education', function (Blueprint $table) {
            $table->boolean('is_visible')->default(true)->after('order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            $table->dropColumn('is_visible');
        });

        Schema::table('education', function (Blueprint $table) {
            $table->dropColumn('is_visible');
        });
    }
};
