<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('testimonials_content', function (Blueprint $table) {
            $table->id();

            // Testimonials section content
            $table->string('section_badge')->default('Testimonials');
            $table->string('section_title')->default('What Clients Say');
            $table->text('section_description')->default('Feedback from clients who have experienced working with me.');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('testimonials_content');
    }
};
