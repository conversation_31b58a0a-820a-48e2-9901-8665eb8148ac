<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Remove contact fields from hero_section table
            $table->dropColumn(['email', 'phone', 'location']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Add contact fields back to hero_section table
            $table->string('email')->nullable()->after('about');
            $table->string('phone')->nullable()->after('email');
            $table->string('location')->nullable()->after('phone');
        });
    }
};
