<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_management', function (Blueprint $table) {
            $table->id();

            // Contact section content
            $table->string('section_badge')->default('Contact');
            $table->string('section_title')->default('Get In Touch');
            $table->text('section_description')->default('Have a project in mind? Let us discuss your ideas and bring them to life.');

            // Contact information
            $table->string('email')->default('<EMAIL>');
            $table->string('phone')->default('+****************');
            $table->string('location')->default('City, Country');

            // Contact form placeholders and labels
            $table->string('form_name_label')->default('Name');
            $table->string('form_name_placeholder')->default('Your Name');
            $table->string('form_email_label')->default('Email');
            $table->string('form_email_placeholder')->default('<EMAIL>');
            $table->string('form_subject_label')->default('Subject');
            $table->string('form_subject_placeholder')->default('Project Subject');
            $table->string('form_message_label')->default('Message');
            $table->string('form_message_placeholder')->default('Your Message');
            $table->string('form_submit_button_text')->default('Send Message');

            // Contact info labels
            $table->string('email_label')->default('Email');
            $table->string('email_subtitle')->default('For general inquiries:');
            $table->string('phone_label')->default('Phone');
            $table->string('phone_subtitle')->default('Available Monday-Friday:');
            $table->string('location_label')->default('Location');
            $table->string('location_subtitle')->default('Based in:');

            // Success/Error messages
            $table->text('success_message')->default('Your message has been sent successfully. I will get back to you soon!');
            $table->text('error_message')->default('Sorry, there was an error sending your message. Please try again.');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_management');
    }
};
