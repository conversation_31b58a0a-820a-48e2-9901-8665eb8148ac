<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Change logo_icon column from VARCHAR to TEXT to support larger SVG data
            $table->text('logo_icon')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Revert back to string (VARCHAR) - note: this may truncate data if it's too long
            $table->string('logo_icon')->change();
        });
    }
};
