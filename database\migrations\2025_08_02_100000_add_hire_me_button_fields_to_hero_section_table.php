<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            // Add hire me button fields for navbar
            $table->string('hire_me_text')->default('Hire Me')->after('navbar_items');
            $table->string('hire_me_url')->default('#contact')->after('hire_me_text');
            $table->boolean('hire_me_enabled')->default(true)->after('hire_me_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hero_section', function (Blueprint $table) {
            $table->dropColumn(['hire_me_text', 'hire_me_url', 'hire_me_enabled']);
        });
    }
};
