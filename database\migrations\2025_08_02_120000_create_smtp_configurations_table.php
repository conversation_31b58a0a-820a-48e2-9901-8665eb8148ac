<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('smtp_configurations', function (Blueprint $table) {
            $table->id();
            $table->boolean('enabled')->default(false);
            $table->string('mailer')->default('smtp');
            $table->string('host')->nullable();
            $table->integer('port')->default(587);
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->string('encryption')->default('tls'); // tls, ssl, or null
            $table->string('from_address')->nullable();
            $table->string('from_name')->nullable();
            $table->string('admin_email')->nullable(); // Admin email for notifications
            $table->boolean('admin_notifications')->default(true); // Enable/disable admin notifications
            $table->text('test_email_subject')->default('Test Email from Portfolio');
            $table->text('test_email_body')->default('This is a test email to verify SMTP configuration is working correctly.');
            $table->timestamp('last_tested_at')->nullable();
            $table->boolean('last_test_successful')->default(false);
            $table->text('last_test_error')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('smtp_configurations');
    }
};
