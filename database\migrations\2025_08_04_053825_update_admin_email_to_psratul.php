<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update admin <NAME_EMAIL> to <EMAIL>
        DB::table('admin')
            ->where('email', '<EMAIL>')
            ->update([
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'), // Ensure password is admin123
                'updated_at' => now()
            ]);

        // <NAME_EMAIL> exists, create the admin user
        $adminExists = DB::table('admin')->where('email', '<EMAIL>')->exists();
        if (!$adminExists) {
            DB::table('admin')->insert([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert admin email <NAME_EMAIL>
        DB::table('admin')
            ->where('email', '<EMAIL>')
            ->update([
                'email' => '<EMAIL>',
                'updated_at' => now()
            ]);
    }
};
