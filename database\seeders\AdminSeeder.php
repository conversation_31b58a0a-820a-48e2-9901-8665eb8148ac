<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    public function run(): void
    {
        // Delete existing admin user if exists
        User::where('email', '<EMAIL>')->delete();

        // Create new admin user
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'email_verified_at' => now(),
        ]);
    }
} 