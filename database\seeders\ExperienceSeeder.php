<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Experience;

class ExperienceSeeder extends Seeder
{
    public function run(): void
    {
        $experiences = [
            [
                'title' => 'Senior Full Stack Developer',
                'company' => 'Tech Innovators Inc.',
                'location' => 'San Francisco, CA',
                'start_date' => '2021-01-01',
                'end_date' => null,
                'is_current' => true,
                'description' => 'Leading development of enterprise web applications using Laravel and React. Managing a team of 5 developers.',
                'type' => 'work',
                'order' => 1,
            ],
            [
                'title' => 'Full Stack Developer',
                'company' => 'Digital Solutions Ltd.',
                'location' => 'New York, NY',
                'start_date' => '2019-03-01',
                'end_date' => '2020-12-31',
                'is_current' => false,
                'description' => 'Developed and maintained multiple client projects using Vue.js and Node.js. Implemented CI/CD pipelines.',
                'type' => 'work',
                'order' => 2,
            ],
            [
                'title' => 'Junior Developer',
                'company' => 'StartUp Hub',
                'location' => 'Boston, MA',
                'start_date' => '2018-06-01',
                'end_date' => '2019-02-28',
                'is_current' => false,
                'description' => 'Worked on frontend development using React and backend API development with Node.js.',
                'type' => 'work',
                'order' => 3,
            ],
            [
                'title' => 'Frontend Developer Intern',
                'company' => 'Creative Agency',
                'location' => 'Austin, TX',
                'start_date' => '2017-06-01',
                'end_date' => '2017-08-31',
                'is_current' => false,
                'description' => 'Assisted in developing responsive websites and mobile applications. Gained experience with modern CSS frameworks and JavaScript libraries.',
                'type' => 'work',
                'order' => 4,
            ],
            [
                'title' => 'Web Development Freelancer',
                'company' => 'Self-Employed',
                'location' => 'Remote',
                'start_date' => '2016-01-01',
                'end_date' => '2018-05-31',
                'is_current' => false,
                'description' => 'Provided web development services to small businesses and startups. Built custom websites using WordPress, HTML, CSS, and JavaScript.',
                'type' => 'work',
                'order' => 5,
            ],
        ];

        foreach ($experiences as $experience) {
            Experience::create($experience);
        }
    }
} 