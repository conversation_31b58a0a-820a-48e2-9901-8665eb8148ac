<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'content' => '<h2>Welcome to Our Company</h2><p>We are a dynamic team of professionals dedicated to creating exceptional digital experiences. Our mission is to help businesses grow through innovative web solutions and cutting-edge technology.</p><h3>Our Story</h3><p>Founded in 2020, we have been at the forefront of digital innovation, helping companies transform their online presence and achieve their business goals.</p><ul><li>Expert team of developers and designers</li><li>Proven track record of successful projects</li><li>Commitment to quality and excellence</li><li>Customer-focused approach</li></ul>',
                'meta_description' => 'Learn more about our company, our mission, and the team behind our success.',
                'meta_keywords' => 'about us, company, team, mission, digital solutions',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'order' => 1,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<h2>Privacy Policy</h2><p>This Privacy Policy describes how we collect, use, and protect your personal information when you visit our website or use our services.</p><h3>Information We Collect</h3><p>We may collect the following types of information:</p><ul><li>Personal identification information (Name, email address, phone number, etc.)</li><li>Usage data and analytics</li><li>Cookies and tracking technologies</li></ul><h3>How We Use Your Information</h3><p>We use the collected information for:</p><ul><li>Providing and improving our services</li><li>Communicating with you</li><li>Analytics and performance monitoring</li></ul><p>We are committed to protecting your privacy and will never sell your personal information to third parties.</p>',
                'meta_description' => 'Read our privacy policy to understand how we collect, use, and protect your personal information.',
                'meta_keywords' => 'privacy policy, data protection, personal information, cookies',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
                'order' => 2,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => '<h2>Terms of Service</h2><p>By accessing and using our website, you accept and agree to be bound by the terms and provision of this agreement.</p><h3>Use License</h3><p>Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.</p><h3>Disclaimer</h3><p>The materials on our website are provided on an "as is" basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p><h3>Limitations</h3><p>In no event shall our company or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on our website.</p>',
                'meta_description' => 'Read our terms of service to understand the rules and regulations for using our website and services.',
                'meta_keywords' => 'terms of service, legal, agreement, website usage, conditions',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
                'order' => 3,
            ],
            [
                'title' => 'Our Services Overview',
                'slug' => 'services-overview',
                'content' => '<h2>Comprehensive Digital Solutions</h2><p>We offer a wide range of digital services to help your business succeed in the modern marketplace.</p><h3>Web Development</h3><p>Custom websites built with the latest technologies and best practices. From simple landing pages to complex web applications, we have you covered.</p><h3>Mobile App Development</h3><p>Native and cross-platform mobile applications that provide exceptional user experiences across all devices.</p><h3>Digital Marketing</h3><p>Strategic marketing campaigns that drive traffic, generate leads, and increase conversions.</p><h3>SEO Optimization</h3><p>Improve your search engine rankings and increase organic traffic with our proven SEO strategies.</p><p><strong>Ready to get started?</strong> Contact us today to discuss your project requirements and get a free consultation.</p>',
                'meta_description' => 'Discover our comprehensive range of digital services including web development, mobile apps, and digital marketing.',
                'meta_keywords' => 'web development, mobile apps, digital marketing, SEO, services',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'order' => 4,
            ],
            [
                'title' => 'Blog Post Draft',
                'slug' => 'blog-post-draft',
                'content' => '<h2>This is a Draft Post</h2><p>This page is currently in draft status and is not visible to the public. It demonstrates how the page management system handles different page statuses.</p><p>Draft pages can be:</p><ul><li>Edited and refined before publishing</li><li>Previewed by administrators</li><li>Scheduled for future publication</li></ul>',
                'meta_description' => 'This is a draft page for testing purposes.',
                'meta_keywords' => 'draft, test, page management',
                'status' => 'draft',
                'is_featured' => false,
                'published_at' => null,
                'order' => 5,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::create($pageData);
        }
    }
}
