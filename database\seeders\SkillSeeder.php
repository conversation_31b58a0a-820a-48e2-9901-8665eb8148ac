<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Skill;

class SkillSeeder extends Seeder
{
    public function run(): void
    {
        $skills = [
            // Progress Bar Skills (Max 5 visible, 10 total)
            [
                'name' => 'Laravel',
                'proficiency' => 95,
                'display_type' => 'progress',
                'is_visible' => true,
                'order' => 1,
            ],
            [
                'name' => 'React',
                'proficiency' => 90,
                'display_type' => 'progress',
                'is_visible' => true,
                'order' => 2,
            ],
            [
                'name' => 'Vue.js',
                'proficiency' => 88,
                'display_type' => 'progress',
                'is_visible' => true,
                'order' => 3,
            ],
            [
                'name' => 'TypeScript',
                'proficiency' => 85,
                'display_type' => 'progress',
                'is_visible' => true,
                'order' => 4,
            ],
            [
                'name' => 'Node.js',
                'proficiency' => 80,
                'display_type' => 'progress',
                'is_visible' => true,
                'order' => 5,
            ],
            // Additional Progress Skills (Initially Hidden)
            [
                'name' => 'PHP',
                'proficiency' => 92,
                'display_type' => 'progress',
                'is_visible' => false,
                'order' => 6,
            ],
            [
                'name' => 'JavaScript',
                'proficiency' => 87,
                'display_type' => 'progress',
                'is_visible' => false,
                'order' => 7,
            ],
            [
                'name' => 'Python',
                'proficiency' => 82,
                'display_type' => 'progress',
                'is_visible' => false,
                'order' => 8,
            ],
            [
                'name' => 'Next.js',
                'proficiency' => 78,
                'display_type' => 'progress',
                'is_visible' => false,
                'order' => 9,
            ],
            [
                'name' => 'Nuxt.js',
                'proficiency' => 75,
                'display_type' => 'progress',
                'is_visible' => false,
                'order' => 10,
            ],

            // Card Skills (Max 6 visible, 12 total)
            [
                'name' => 'Figma',
                'proficiency' => 90,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 1,
            ],
            [
                'name' => 'GraphQL',
                'proficiency' => 75,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 2,
            ],
            [
                'name' => 'Responsive Design',
                'proficiency' => 95,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 3,
            ],
            [
                'name' => 'Git',
                'proficiency' => 85,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 4,
            ],
            [
                'name' => 'MySQL',
                'proficiency' => 80,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 5,
            ],
            [
                'name' => 'Docker',
                'proficiency' => 70,
                'display_type' => 'card',
                'is_visible' => true,
                'order' => 6,
            ],
            // Additional Card Skills (Initially Hidden)
            [
                'name' => 'Adobe XD',
                'proficiency' => 88,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 7,
            ],
            [
                'name' => 'MongoDB',
                'proficiency' => 83,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 8,
            ],
            [
                'name' => 'Redis',
                'proficiency' => 78,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 9,
            ],
            [
                'name' => 'AWS',
                'proficiency' => 72,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 10,
            ],
            [
                'name' => 'Tailwind CSS',
                'proficiency' => 90,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 11,
            ],
            [
                'name' => 'REST APIs',
                'proficiency' => 85,
                'display_type' => 'card',
                'is_visible' => false,
                'order' => 12,
            ],
        ];

        foreach ($skills as $skill) {
            Skill::create($skill);
        }
    }
} 