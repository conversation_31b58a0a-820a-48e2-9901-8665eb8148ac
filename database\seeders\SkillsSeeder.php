<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Skill;

class SkillsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing skills
        Skill::truncate();

        // Progress skills (left side bars)
        $progressSkills = [
            ['name' => 'HTML/CSS', 'proficiency' => 95, 'category' => 'Frontend', 'display_type' => 'progress', 'order' => 1],
            ['name' => 'JavaScript', 'proficiency' => 90, 'category' => 'Frontend', 'display_type' => 'progress', 'order' => 2],
            ['name' => 'React', 'proficiency' => 85, 'category' => 'Frontend', 'display_type' => 'progress', 'order' => 3],
            ['name' => 'Next.js', 'proficiency' => 80, 'category' => 'Frontend', 'display_type' => 'progress', 'order' => 4],
            ['name' => 'UI/UX Design', 'proficiency' => 85, 'category' => 'Design', 'display_type' => 'progress', 'order' => 5],
        ];

        // Card skills (right side cards)
        $cardSkills = [
            ['name' => 'Node.js', 'proficiency' => 75, 'category' => 'Backend', 'display_type' => 'card', 'order' => 1],
            ['name' => 'TypeScript', 'proficiency' => 85, 'category' => 'Frontend', 'display_type' => 'card', 'order' => 2],
            ['name' => 'Figma', 'proficiency' => 90, 'category' => 'Design', 'display_type' => 'card', 'order' => 3],
            ['name' => 'GraphQL', 'proficiency' => 75, 'category' => 'Backend', 'display_type' => 'card', 'order' => 4],
            ['name' => 'Responsive Design', 'proficiency' => 95, 'category' => 'Frontend', 'display_type' => 'card', 'order' => 5],
            ['name' => 'Git', 'proficiency' => 80, 'category' => 'Tools', 'display_type' => 'card', 'order' => 6],
        ];

        // Insert progress skills
        foreach ($progressSkills as $skill) {
            Skill::create([
                'name' => $skill['name'],
                'proficiency' => $skill['proficiency'],
                'category' => $skill['category'],
                'icon' => 'default', // Default icon
                'display_type' => $skill['display_type'],
                'order' => $skill['order'],
                'is_visible' => true,
            ]);
        }

        // Insert card skills
        foreach ($cardSkills as $skill) {
            Skill::create([
                'name' => $skill['name'],
                'proficiency' => $skill['proficiency'],
                'category' => $skill['category'],
                'icon' => 'default', // Default icon
                'display_type' => $skill['display_type'],
                'order' => $skill['order'],
                'is_visible' => true,
            ]);
        }
    }
}
