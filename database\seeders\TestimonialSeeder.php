<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON>',
                'position' => 'CEO',
                'company' => 'Tech Solutions Inc.',
                'quote' => 'Exceptional work! Delivered our project on time and exceeded our expectations. Great communication throughout the process.',
                'rating' => 5,
                'image' => null,
                'is_featured' => true,
                'order' => 1,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Product Manager',
                'company' => 'Digital Innovations',
                'quote' => 'Outstanding developer with excellent problem-solving skills. Helped us create a beautiful and functional web application.',
                'rating' => 5,
                'image' => null,
                'is_featured' => true,
                'order' => 2,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'CTO',
                'company' => 'StartUp Hub',
                'quote' => 'Highly skilled and professional. Great attention to detail and always delivers high-quality work.',
                'rating' => 5,
                'image' => null,
                'is_featured' => true,
                'order' => 3,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Founder',
                'company' => 'DesignHub',
                'quote' => 'Exceptional work ethic and communication throughout the project. The final product was exactly what we envisioned and more.',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 4,
            ],
            [
                'name' => 'David Thompson',
                'position' => 'Marketing Director',
                'company' => 'GrowthLabs',
                'quote' => 'The attention to detail and creative solutions provided made our website stand out from competitors. Highly recommended!',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 5,
            ],
            [
                'name' => 'Lisa Wang',
                'position' => 'Operations Manager',
                'company' => 'InnovateCorp',
                'quote' => 'Professional, reliable, and delivered beyond our expectations. The project was completed ahead of schedule with excellent quality.',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 6,
            ],
            [
                'name' => 'Robert Martinez',
                'position' => 'Technical Lead',
                'company' => 'CodeCraft Solutions',
                'quote' => 'Impressive technical skills and ability to understand complex requirements. The solution provided was both elegant and efficient.',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 7,
            ],
            [
                'name' => 'Amanda Foster',
                'position' => 'Project Manager',
                'company' => 'WebFlow Agency',
                'quote' => 'Great collaboration and communication skills. Made the entire development process smooth and stress-free for our team.',
                'rating' => 4,
                'image' => null,
                'is_featured' => false,
                'order' => 8,
            ],
            [
                'name' => 'James Wilson',
                'position' => 'Business Owner',
                'company' => 'Local Retail Co.',
                'quote' => 'Transformed our online presence completely. Sales increased by 40% after launching the new website. Fantastic work!',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 9,
            ],
            [
                'name' => 'Rachel Green',
                'position' => 'Creative Director',
                'company' => 'Artistic Ventures',
                'quote' => 'Brought our creative vision to life with technical excellence. The perfect blend of aesthetics and functionality.',
                'rating' => 5,
                'image' => null,
                'is_featured' => false,
                'order' => 10,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
} 