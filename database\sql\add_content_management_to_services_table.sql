-- Add content management fields to existing services table
ALTER TABLE `services` 
ADD COLUMN `is_content_management` BO<PERSON>EAN NOT NULL DEFAULT FALSE AFTER `order`,
ADD COLUMN `services_section_badge` VARCHAR(255) NULL AFTER `is_content_management`,
ADD COLUMN `services_section_title` VARCHAR(255) NULL AFTER `services_section_badge`,
ADD COLUMN `services_section_description` TEXT NULL AFTER `services_section_title`,
ADD COLUMN `services_button_text` VARCHAR(255) NULL AFTER `services_section_description`,
ADD COLUMN `services_page_title` VARCHAR(255) NULL AFTER `services_button_text`,
ADD COLUMN `services_page_description` TEXT NULL AFTER `services_page_title`,
ADD COLUMN `services_benefit_1_text` VARCHAR(255) NULL AFTER `services_page_description`,
ADD COLUMN `services_benefit_1_icon` VARCHAR(255) NULL AFTER `services_benefit_1_text`,
ADD COLUMN `services_benefit_2_text` VARCHAR(255) NULL AFTER `services_benefit_1_icon`,
ADD COLUMN `services_benefit_2_icon` VARCHAR(255) NULL AFTER `services_benefit_2_text`,
ADD COLUMN `services_benefit_3_text` VARCHAR(255) NULL AFTER `services_benefit_2_icon`,
ADD COLUMN `services_benefit_3_icon` VARCHAR(255) NULL AFTER `services_benefit_3_text`,
ADD COLUMN `work_process_title` VARCHAR(255) NULL AFTER `services_benefit_3_icon`,
ADD COLUMN `work_process_description` TEXT NULL AFTER `work_process_title`,
ADD COLUMN `work_process_steps` JSON NULL AFTER `work_process_description`,
ADD COLUMN `services_cta_title` VARCHAR(255) NULL AFTER `work_process_steps`,
ADD COLUMN `services_cta_description` TEXT NULL AFTER `services_cta_title`,
ADD COLUMN `services_cta_primary_text` VARCHAR(255) NULL AFTER `services_cta_description`,
ADD COLUMN `services_cta_secondary_text` VARCHAR(255) NULL AFTER `services_cta_primary_text`;

-- Insert content management record with data from profiles table (if exists)
INSERT INTO `services` (
    `title`,
    `description`,
    `icon`,
    `price`,
    `starting_price`,
    `features`,
    `is_active`,
    `is_featured`,
    `is_content_management`,
    `order`,
    `services_section_badge`,
    `services_section_title`,
    `services_section_description`,
    `services_button_text`,
    `services_page_title`,
    `services_page_description`,
    `services_benefit_1_text`,
    `services_benefit_1_icon`,
    `services_benefit_2_text`,
    `services_benefit_2_icon`,
    `services_benefit_3_text`,
    `services_benefit_3_icon`,
    `work_process_title`,
    `work_process_description`,
    `work_process_steps`,
    `services_cta_title`,
    `services_cta_description`,
    `services_cta_primary_text`,
    `services_cta_secondary_text`,
    `created_at`,
    `updated_at`
)
SELECT 
    'Content Management' as title,
    'This record stores all services content management data' as description,
    'Settings' as icon,
    0 as price,
    0 as starting_price,
    JSON_ARRAY() as features,
    FALSE as is_active,
    FALSE as is_featured,
    TRUE as is_content_management,
    0 as `order`,
    COALESCE(services_section_badge, 'Professional Services') as services_section_badge,
    COALESCE(services_section_title, 'Areas of Expertise') as services_section_title,
    COALESCE(services_section_description, 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape') as services_section_description,
    COALESCE(services_button_text, 'Explore All Services') as services_button_text,
    COALESCE(services_page_title, 'Professional Services') as services_page_title,
    COALESCE(services_page_description, 'Comprehensive digital solutions tailored to your business needs.') as services_page_description,
    COALESCE(services_benefit_1_text, 'Fast Delivery') as services_benefit_1_text,
    COALESCE(services_benefit_1_icon, 'Zap') as services_benefit_1_icon,
    COALESCE(services_benefit_2_text, 'Quality Guaranteed') as services_benefit_2_text,
    COALESCE(services_benefit_2_icon, 'CheckCircle') as services_benefit_2_icon,
    COALESCE(services_benefit_3_text, '24/7 Support') as services_benefit_3_text,
    COALESCE(services_benefit_3_icon, 'Clock') as services_benefit_3_icon,
    COALESCE(work_process_title, 'My Work Process') as work_process_title,
    COALESCE(work_process_description, 'A systematic approach that ensures quality results and client satisfaction.') as work_process_description,
    COALESCE(work_process_steps, JSON_ARRAY(
        JSON_OBJECT('number', '01', 'title', 'Discovery', 'description', 'Understanding your requirements, goals, and target audience to create an optimal solution.'),
        JSON_OBJECT('number', '02', 'title', 'Planning', 'description', 'Creating detailed project plans, wireframes, and technical specifications.'),
        JSON_OBJECT('number', '03', 'title', 'Development', 'description', 'Building your solution using best practices and cutting-edge technologies.'),
        JSON_OBJECT('number', '04', 'title', 'Testing', 'description', 'Rigorous testing across all devices and browsers to ensure quality and performance.'),
        JSON_OBJECT('number', '05', 'title', 'Launch', 'description', 'Deploying your project and providing ongoing support and maintenance.')
    )) as work_process_steps,
    COALESCE(services_cta_title, 'Ready to Start Your Project?') as services_cta_title,
    COALESCE(services_cta_description, 'Let us discuss your requirements and create something amazing together.') as services_cta_description,
    COALESCE(services_cta_primary_text, 'Get Free Consultation') as services_cta_primary_text,
    COALESCE(services_cta_secondary_text, 'View Portfolio') as services_cta_secondary_text,
    NOW() as created_at,
    NOW() as updated_at
FROM profiles 
WHERE NOT EXISTS (SELECT 1 FROM services WHERE is_content_management = TRUE)
LIMIT 1;

-- If no profiles exist, insert default content management record
INSERT INTO `services` (
    `title`,
    `description`,
    `icon`,
    `price`,
    `starting_price`,
    `features`,
    `is_active`,
    `is_featured`,
    `is_content_management`,
    `order`,
    `services_section_badge`,
    `services_section_title`,
    `services_section_description`,
    `services_button_text`,
    `services_page_title`,
    `services_page_description`,
    `services_benefit_1_text`,
    `services_benefit_1_icon`,
    `services_benefit_2_text`,
    `services_benefit_2_icon`,
    `services_benefit_3_text`,
    `services_benefit_3_icon`,
    `work_process_title`,
    `work_process_description`,
    `work_process_steps`,
    `services_cta_title`,
    `services_cta_description`,
    `services_cta_primary_text`,
    `services_cta_secondary_text`,
    `created_at`,
    `updated_at`
)
SELECT 
    'Content Management',
    'This record stores all services content management data',
    'Settings',
    0,
    0,
    JSON_ARRAY(),
    FALSE,
    FALSE,
    TRUE,
    0,
    'Professional Services',
    'Areas of Expertise',
    'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape',
    'Explore All Services',
    'Professional Services',
    'Comprehensive digital solutions tailored to your business needs.',
    'Fast Delivery',
    'Zap',
    'Quality Guaranteed',
    'CheckCircle',
    '24/7 Support',
    'Clock',
    'My Work Process',
    'A systematic approach that ensures quality results and client satisfaction.',
    JSON_ARRAY(
        JSON_OBJECT('number', '01', 'title', 'Discovery', 'description', 'Understanding your requirements, goals, and target audience to create an optimal solution.'),
        JSON_OBJECT('number', '02', 'title', 'Planning', 'description', 'Creating detailed project plans, wireframes, and technical specifications.'),
        JSON_OBJECT('number', '03', 'title', 'Development', 'description', 'Building your solution using best practices and cutting-edge technologies.'),
        JSON_OBJECT('number', '04', 'title', 'Testing', 'description', 'Rigorous testing across all devices and browsers to ensure quality and performance.'),
        JSON_OBJECT('number', '05', 'title', 'Launch', 'description', 'Deploying your project and providing ongoing support and maintenance.')
    ),
    'Ready to Start Your Project?',
    'Let us discuss your requirements and create something amazing together.',
    'Get Free Consultation',
    'View Portfolio',
    NOW(),
    NOW()
WHERE NOT EXISTS (SELECT 1 FROM services WHERE is_content_management = TRUE);
