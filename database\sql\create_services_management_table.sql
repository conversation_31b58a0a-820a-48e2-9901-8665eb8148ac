-- Create services_management table
CREATE TABLE IF NOT EXISTS `services_management` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `services_section_badge` varchar(255) NOT NULL DEFAULT 'Professional Services',
  `services_section_title` varchar(255) NOT NULL DEFAULT 'Areas of Expertise',
  `services_section_description` text NOT NULL DEFAULT 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape',
  `services_button_text` varchar(255) NOT NULL DEFAULT 'Explore All Services',
  `services_page_title` varchar(255) NOT NULL DEFAULT 'Professional Services',
  `services_page_description` text NOT NULL DEFAULT 'Comprehensive digital solutions tailored to your business needs.',
  `services_benefit_1_text` varchar(255) NOT NULL DEFAULT 'Fast Delivery',
  `services_benefit_1_icon` varchar(255) NOT NULL DEFAULT 'Zap',
  `services_benefit_2_text` varchar(255) NOT NULL DEFAULT 'Quality Guaranteed',
  `services_benefit_2_icon` varchar(255) NOT NULL DEFAULT 'CheckCircle',
  `services_benefit_3_text` varchar(255) NOT NULL DEFAULT '24/7 Support',
  `services_benefit_3_icon` varchar(255) NOT NULL DEFAULT 'Clock',
  `work_process_title` varchar(255) NOT NULL DEFAULT 'My Work Process',
  `work_process_description` text NOT NULL DEFAULT 'A systematic approach that ensures quality results and client satisfaction.',
  `work_process_steps` json DEFAULT NULL,
  `services_cta_title` varchar(255) NOT NULL DEFAULT 'Ready to Start Your Project?',
  `services_cta_description` text NOT NULL DEFAULT 'Let us discuss your requirements and create something amazing together.',
  `services_cta_primary_text` varchar(255) NOT NULL DEFAULT 'Get Free Consultation',
  `services_cta_secondary_text` varchar(255) NOT NULL DEFAULT 'View Portfolio',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default data or migrate from profiles table
INSERT INTO `services_management` (
  `services_section_badge`,
  `services_section_title`, 
  `services_section_description`,
  `services_button_text`,
  `services_page_title`,
  `services_page_description`,
  `services_benefit_1_text`,
  `services_benefit_1_icon`,
  `services_benefit_2_text`,
  `services_benefit_2_icon`,
  `services_benefit_3_text`,
  `services_benefit_3_icon`,
  `work_process_title`,
  `work_process_description`,
  `work_process_steps`,
  `services_cta_title`,
  `services_cta_description`,
  `services_cta_primary_text`,
  `services_cta_secondary_text`,
  `created_at`,
  `updated_at`
) 
SELECT 
  COALESCE(services_section_badge, 'Professional Services'),
  COALESCE(services_section_title, 'Areas of Expertise'),
  COALESCE(services_section_description, 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape'),
  COALESCE(services_button_text, 'Explore All Services'),
  COALESCE(services_page_title, 'Professional Services'),
  COALESCE(services_page_description, 'Comprehensive digital solutions tailored to your business needs.'),
  COALESCE(services_benefit_1_text, 'Fast Delivery'),
  COALESCE(services_benefit_1_icon, 'Zap'),
  COALESCE(services_benefit_2_text, 'Quality Guaranteed'),
  COALESCE(services_benefit_2_icon, 'CheckCircle'),
  COALESCE(services_benefit_3_text, '24/7 Support'),
  COALESCE(services_benefit_3_icon, 'Clock'),
  COALESCE(work_process_title, 'My Work Process'),
  COALESCE(work_process_description, 'A systematic approach that ensures quality results and client satisfaction.'),
  COALESCE(work_process_steps, JSON_ARRAY(
    JSON_OBJECT('number', '01', 'title', 'Discovery', 'description', 'Understanding your requirements, goals, and target audience to create an optimal solution.'),
    JSON_OBJECT('number', '02', 'title', 'Planning', 'description', 'Creating detailed project plans, wireframes, and technical specifications.'),
    JSON_OBJECT('number', '03', 'title', 'Development', 'description', 'Building your solution using best practices and cutting-edge technologies.'),
    JSON_OBJECT('number', '04', 'title', 'Testing', 'description', 'Rigorous testing across all devices and browsers to ensure quality and performance.'),
    JSON_OBJECT('number', '05', 'title', 'Launch', 'description', 'Deploying your project and providing ongoing support and maintenance.')
  )),
  COALESCE(services_cta_title, 'Ready to Start Your Project?'),
  COALESCE(services_cta_description, 'Let us discuss your requirements and create something amazing together.'),
  COALESCE(services_cta_primary_text, 'Get Free Consultation'),
  COALESCE(services_cta_secondary_text, 'View Portfolio'),
  NOW(),
  NOW()
FROM profiles 
LIMIT 1;

-- If no profiles exist, insert default data
INSERT INTO `services_management` (
  `services_section_badge`,
  `services_section_title`, 
  `services_section_description`,
  `services_button_text`,
  `services_page_title`,
  `services_page_description`,
  `services_benefit_1_text`,
  `services_benefit_1_icon`,
  `services_benefit_2_text`,
  `services_benefit_2_icon`,
  `services_benefit_3_text`,
  `services_benefit_3_icon`,
  `work_process_title`,
  `work_process_description`,
  `work_process_steps`,
  `services_cta_title`,
  `services_cta_description`,
  `services_cta_primary_text`,
  `services_cta_secondary_text`,
  `created_at`,
  `updated_at`
)
SELECT 
  'Professional Services',
  'Areas of Expertise',
  'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape',
  'Explore All Services',
  'Professional Services',
  'Comprehensive digital solutions tailored to your business needs.',
  'Fast Delivery',
  'Zap',
  'Quality Guaranteed',
  'CheckCircle',
  '24/7 Support',
  'Clock',
  'My Work Process',
  'A systematic approach that ensures quality results and client satisfaction.',
  JSON_ARRAY(
    JSON_OBJECT('number', '01', 'title', 'Discovery', 'description', 'Understanding your requirements, goals, and target audience to create an optimal solution.'),
    JSON_OBJECT('number', '02', 'title', 'Planning', 'description', 'Creating detailed project plans, wireframes, and technical specifications.'),
    JSON_OBJECT('number', '03', 'title', 'Development', 'description', 'Building your solution using best practices and cutting-edge technologies.'),
    JSON_OBJECT('number', '04', 'title', 'Testing', 'description', 'Rigorous testing across all devices and browsers to ensure quality and performance.'),
    JSON_OBJECT('number', '05', 'title', 'Launch', 'description', 'Deploying your project and providing ongoing support and maintenance.')
  ),
  'Ready to Start Your Project?',
  'Let us discuss your requirements and create something amazing together.',
  'Get Free Consultation',
  'View Portfolio',
  NOW(),
  NOW()
WHERE NOT EXISTS (SELECT 1 FROM services_management);
