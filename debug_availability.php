<?php

// Load Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\HeroSection;

// Get the hero section
$heroSection = HeroSection::getOrCreate();

echo "=== Hero Section Debug Info ===\n";
echo "ID: " . $heroSection->id . "\n";
echo "Full Name: " . $heroSection->full_name . "\n";
echo "Is Available (raw): ";
var_dump($heroSection->is_available);
echo "Is Available (boolean): " . ($heroSection->is_available ? 'true' : 'false') . "\n";
echo "Is Available (type): " . gettype($heroSection->is_available) . "\n";

// Test the WelcomeController logic
$heroSectionData = [
    'is_available' => $heroSection->is_available,
];

echo "\n=== WelcomeController Data ===\n";
echo "Passed to frontend: ";
var_dump($heroSectionData['is_available']);

// Test the frontend logic
$profile = (object) $heroSectionData;
$defaultIsAvailable = true;

// Old logic (problematic)
$isAvailableOld = $profile->is_available ?? $defaultIsAvailable;
echo "\n=== Frontend Logic Test ===\n";
echo "Old logic result: ";
var_dump($isAvailableOld);
echo "Old logic (boolean): " . ($isAvailableOld ? 'true' : 'false') . "\n";

// New logic (fixed)
$isAvailableNew = $profile->is_available !== null ? $profile->is_available : $defaultIsAvailable;
echo "New logic result: ";
var_dump($isAvailableNew);
echo "New logic (boolean): " . ($isAvailableNew ? 'true' : 'false') . "\n";

echo "\n=== Summary ===\n";
echo "Database value: " . ($heroSection->is_available ? 'true' : 'false') . "\n";
echo "Old frontend logic: " . ($isAvailableOld ? 'ENABLED' : 'DISABLED') . "\n";
echo "New frontend logic: " . ($isAvailableNew ? 'ENABLED' : 'DISABLED') . "\n";

if ($heroSection->is_available === false && $isAvailableOld === true) {
    echo "\n🐛 BUG CONFIRMED: Database shows disabled but old logic shows enabled!\n";
} else {
    echo "\n✅ No bug detected with current data.\n";
}

if ($heroSection->is_available === $isAvailableNew) {
    echo "✅ New logic correctly matches database value.\n";
} else {
    echo "❌ New logic still has issues.\n";
}
