<?php

// Force sync categories from projects table to projects_management table

require_once 'vendor/autoload.php';

// Load <PERSON> app
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Project;
use App\Models\ProjectsManagement;

echo "🔄 Force syncing categories from projects table...\n\n";

try {
    // Get all unique categories from projects table (all projects including drafts)
    $allProjectCategories = Project::getAllUniqueCategories();
    echo "📋 All project categories (including drafts): " . (empty($allProjectCategories) ? '(none)' : implode(', ', $allProjectCategories)) . "\n";

    // Get published project categories only
    $publishedProjectCategories = Project::getUniqueCategories();
    echo "📋 Published project categories: " . (empty($publishedProjectCategories) ? '(none)' : implode(', ', $publishedProjectCategories)) . "\n";

    // Get or create projects_management record
    $contentManagement = ProjectsManagement::first();
    if (!$contentManagement) {
        $contentManagement = ProjectsManagement::getOrCreate();
        echo "✨ Created new projects_management record.\n";
    } else {
        echo "📄 Using existing projects_management record (ID: {$contentManagement->id}).\n";
    }

    // Show current filter_categories
    $currentCategories = $contentManagement->filter_categories;
    echo "🔍 Current filter_categories: " . (empty($currentCategories) ? '(empty)' : json_encode($currentCategories)) . "\n";

    // Force update with all project categories (including drafts for admin use)
    $contentManagement->update([
        'filter_categories' => $allProjectCategories
    ]);

    echo "✅ Successfully force synced categories!\n";
    echo "🎯 New filter_categories: " . (empty($allProjectCategories) ? '(empty)' : json_encode($allProjectCategories)) . "\n\n";

    echo "🚀 What this means:\n";
    echo "1. Admin panel category filter will show: " . (empty($allProjectCategories) ? '(no categories)' : implode(', ', $allProjectCategories)) . "\n";
    echo "2. Public projects page will show: " . (empty($publishedProjectCategories) ? '(no categories)' : implode(', ', $publishedProjectCategories)) . "\n";
    echo "3. Categories are now 100% dynamic from projects table\n";
    echo "4. No more hardcoded categories!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📝 Stack trace:\n" . $e->getTraceAsString() . "\n";
}
