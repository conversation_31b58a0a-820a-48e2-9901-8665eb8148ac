-- Manual sync of project categories from projects table to projects_management table
-- Run this in phpMyAdmin to initially sync existing project categories

-- Step 1: See current categories in projects table
SELECT DISTINCT category FROM projects WHERE category IS NOT NULL AND category != '' ORDER BY category;

-- Step 2: See current filter_categories in projects_management table
SELECT id, filter_categories FROM projects_management;

-- Step 3: Get unique categories as JSON array (for manual update)
-- You'll need to copy the result and use it in Step 4
SELECT CONCAT('["', GROUP_CONCAT(DISTINCT category SEPARATOR '","'), '"]') as categories_json
FROM projects 
WHERE category IS NOT NULL AND category != '' 
ORDER BY category;

-- Step 4: Update projects_management with actual project categories
-- Replace the JSON array below with the result from Step 3
-- Example: UPDATE projects_management SET filter_categories = '["E-commerce","Mobile App","UI/UX Design","Web Design"]';

-- UPDATE projects_management SET filter_categories = 'REPLACE_WITH_RESULT_FROM_STEP_3';

-- Step 5: Verify the update
SELECT id, filter_categories FROM projects_management;
