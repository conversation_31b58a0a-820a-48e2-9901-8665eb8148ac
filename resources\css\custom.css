/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* Smooth scrolling for admin layout */
.admin-content-scroll {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.admin-content-scroll::-webkit-scrollbar {
    width: 6px;
}

.admin-content-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.admin-content-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.admin-content-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Prevent body scrolling in admin layout */
.admin-layout-body {
    overflow: hidden;
    height: 100vh;
}