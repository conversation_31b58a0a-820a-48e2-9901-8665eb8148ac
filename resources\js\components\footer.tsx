import { motion } from 'framer-motion';
import { Github, Linkedin, Twitter, Facebook, Instagram, Mail, Phone, MapPin, ArrowUp } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface FooterProps {
    profile?: {
        logo_text?: string;
        social_links?: {
            github?: string;
            linkedin?: string;
            twitter?: string;
            facebook?: string;
            instagram?: string;
        };
        contact?: {
            email?: string;
            phone?: string;
            location?: string;
        };
    };
}

export function Footer({ profile }: FooterProps) {
    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const socialLinks = [
        {
            name: 'GitHub',
            icon: Github,
            url: profile?.social_links?.github,
            color: 'hover:text-gray-900 dark:hover:text-white'
        },
        {
            name: 'LinkedIn',
            icon: Linkedin,
            url: profile?.social_links?.linkedin,
            color: 'hover:text-blue-600'
        },
        {
            name: 'Twitter',
            icon: Twitter,
            url: profile?.social_links?.twitter,
            color: 'hover:text-blue-400'
        },
        {
            name: 'Facebook',
            icon: Facebook,
            url: profile?.social_links?.facebook,
            color: 'hover:text-blue-600'
        },
        {
            name: 'Instagram',
            icon: Instagram,
            url: profile?.social_links?.instagram,
            color: 'hover:text-pink-600'
        }
    ].filter(link => link.url);

    const quickLinks = [
        { name: 'Home', href: '/' },
        { name: 'Services', href: '/services' },
        { name: 'Projects', href: '/projects' },
        { name: 'Pages', href: '/pages' },
        { name: 'Contact', href: '/#contact' }
    ];

    return (
        <footer className="bg-[#0F172A] dark:bg-gray-950 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-gradient-to-br from-[#20B2AA]/20 to-transparent"></div>
                <div className="absolute top-0 left-0 w-full h-full" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
            </div>

            <div className="relative">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {/* Brand Section */}
                        <div className="lg:col-span-2">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                                viewport={{ once: true }}
                            >
                                <div className="flex items-center gap-2 mb-4">
                                    <div className="w-8 h-8 bg-[#20B2AA] rounded-full flex items-center justify-center text-white font-bold">
                                        {profile?.logo_text?.charAt(0) || 'P'}
                                    </div>
                                    <span className="font-bold text-xl">{profile?.logo_text || 'Portfolio'}</span>
                                </div>
                                <p className="text-gray-400 max-w-md mb-6 leading-relaxed">
                                    Creating exceptional digital experiences through innovative design and development solutions. 
                                    Let's build something amazing together.
                                </p>
                                
                                {/* Social Links */}
                                {socialLinks.length > 0 && (
                                    <div className="flex items-center gap-4">
                                        {socialLinks.map((social, index) => (
                                            <motion.a
                                                key={social.name}
                                                href={social.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                initial={{ opacity: 0, scale: 0 }}
                                                whileInView={{ opacity: 1, scale: 1 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                whileHover={{ scale: 1.1 }}
                                                whileTap={{ scale: 0.95 }}
                                                className={`w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-400 transition-all duration-300 ${social.color} hover:bg-gray-700`}
                                                viewport={{ once: true }}
                                            >
                                                <social.icon className="w-5 h-5" />
                                            </motion.a>
                                        ))}
                                    </div>
                                )}
                            </motion.div>
                        </div>

                        {/* Quick Links */}
                        <div>
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
                                <ul className="space-y-3">
                                    {quickLinks.map((link, index) => (
                                        <motion.li
                                            key={link.name}
                                            initial={{ opacity: 0, x: -20 }}
                                            whileInView={{ opacity: 1, x: 0 }}
                                            transition={{ duration: 0.3, delay: index * 0.1 }}
                                            viewport={{ once: true }}
                                        >
                                            {link.href.startsWith('#') ? (
                                                <a
                                                    href={link.href}
                                                    className="text-gray-400 hover:text-[#20B2AA] transition-colors duration-300 flex items-center gap-2 group"
                                                >
                                                    <span className="w-1 h-1 bg-[#20B2AA] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                                                    {link.name}
                                                </a>
                                            ) : (
                                                <Link
                                                    href={link.href}
                                                    className="text-gray-400 hover:text-[#20B2AA] transition-colors duration-300 flex items-center gap-2 group"
                                                >
                                                    <span className="w-1 h-1 bg-[#20B2AA] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                                                    {link.name}
                                                </Link>
                                            )}
                                        </motion.li>
                                    ))}
                                </ul>
                            </motion.div>
                        </div>

                        {/* Contact Info */}
                        <div>
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                viewport={{ once: true }}
                            >
                                <h3 className="font-semibold text-lg mb-4">Get in Touch</h3>
                                <div className="space-y-3">
                                    {profile?.contact?.email && (
                                        <div className="flex items-center gap-3 text-gray-400">
                                            <Mail className="w-4 h-4 text-[#20B2AA]" />
                                            <a
                                                href={`mailto:${profile.contact.email}`}
                                                className="hover:text-[#20B2AA] transition-colors duration-300"
                                            >
                                                {profile.contact.email}
                                            </a>
                                        </div>
                                    )}
                                    {profile?.contact?.phone && (
                                        <div className="flex items-center gap-3 text-gray-400">
                                            <Phone className="w-4 h-4 text-[#20B2AA]" />
                                            <a
                                                href={`tel:${profile.contact.phone}`}
                                                className="hover:text-[#20B2AA] transition-colors duration-300"
                                            >
                                                {profile.contact.phone}
                                            </a>
                                        </div>
                                    )}
                                    {profile?.contact?.location && (
                                        <div className="flex items-center gap-3 text-gray-400">
                                            <MapPin className="w-4 h-4 text-[#20B2AA]" />
                                            <span>{profile.contact.location}</span>
                                        </div>
                                    )}
                                </div>
                            </motion.div>
                        </div>
                    </div>

                    {/* Bottom Section */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.6 }}
                        viewport={{ once: true }}
                        className="border-t border-gray-800 mt-12 pt-8 flex flex-col sm:flex-row items-center justify-between gap-4"
                    >
                        <p className="text-gray-400 text-sm text-center sm:text-left">
                            &copy; {new Date().getFullYear()} {profile?.logo_text || 'Portfolio'}. All rights reserved.
                        </p>
                        
                        <motion.button
                            onClick={scrollToTop}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="w-10 h-10 bg-[#20B2AA] rounded-full flex items-center justify-center text-white hover:bg-[#1a9994] transition-colors duration-300 shadow-lg"
                        >
                            <ArrowUp className="w-5 h-5" />
                        </motion.button>
                    </motion.div>
                </div>
            </div>
        </footer>
    );
}
