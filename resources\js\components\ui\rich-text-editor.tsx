import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
    Bold,
    Italic,
    Underline,
    List,
    ListOrdered,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Link,
    Unlink,
    Type,
    Heading1,
    Heading2,
    Heading3
} from 'lucide-react';

interface RichTextEditorProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    error?: string;
    disabled?: boolean;
}

export function RichTextEditor({
    value,
    onChange,
    placeholder = "Enter your content...",
    className,
    error,
    disabled = false,
}: RichTextEditorProps) {
    const editorRef = useRef<HTMLDivElement>(null);
    const isUpdating = useRef(false);

    // Update editor content when value prop changes
    useEffect(() => {
        if (editorRef.current && !isUpdating.current) {
            editorRef.current.innerHTML = value;
        }
    }, [value]);

    const handleInput = () => {
        if (editorRef.current && !disabled) {
            isUpdating.current = true;
            const content = editorRef.current.innerHTML;
            onChange(content);
            setTimeout(() => {
                isUpdating.current = false;
            }, 0);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (disabled) {
            e.preventDefault();
            return;
        }

        // Handle common formatting shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'b':
                    e.preventDefault();
                    document.execCommand('bold');
                    handleInput();
                    break;
                case 'i':
                    e.preventDefault();
                    document.execCommand('italic');
                    handleInput();
                    break;
                case 'u':
                    e.preventDefault();
                    document.execCommand('underline');
                    handleInput();
                    break;
            }
        }
    };

    const formatText = (command: string, value?: string) => {
        if (disabled) return;
        
        document.execCommand(command, false, value);
        editorRef.current?.focus();
        handleInput();
    };

    const insertList = (ordered: boolean) => {
        if (disabled) return;

        const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';
        document.execCommand(command);
        editorRef.current?.focus();
        handleInput();
    };

    const applyHeading = (headingTag: 'h1' | 'h2' | 'h3') => {
        if (disabled || !editorRef.current) return;

        const selection = window.getSelection();
        if (!selection) return;

        // If no selection, create a new heading at cursor position
        if (selection.rangeCount === 0 || selection.isCollapsed) {
            const headingElement = document.createElement(headingTag);
            headingElement.textContent = 'Heading';

            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                range.insertNode(headingElement);

                // Select the text so user can immediately type
                const newRange = document.createRange();
                newRange.selectNodeContents(headingElement);
                selection.removeAllRanges();
                selection.addRange(newRange);
            } else {
                editorRef.current.appendChild(headingElement);

                // Focus and select the heading
                const newRange = document.createRange();
                newRange.selectNodeContents(headingElement);
                selection.removeAllRanges();
                selection.addRange(newRange);
            }

            handleInput();
            return;
        }

        const range = selection.getRangeAt(0);
        let container = range.commonAncestorContainer;

        // If the container is a text node, get its parent element
        if (container.nodeType === Node.TEXT_NODE) {
            container = container.parentNode!;
        }

        // Find the block-level parent element
        let blockElement = container as Element;
        while (blockElement && blockElement !== editorRef.current) {
            if (blockElement.tagName && ['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(blockElement.tagName)) {
                break;
            }
            blockElement = blockElement.parentNode as Element;
        }

        // If no block element found, create one
        if (!blockElement || blockElement === editorRef.current) {
            // Wrap the selection in a new heading
            const headingElement = document.createElement(headingTag);
            try {
                range.surroundContents(headingElement);
            } catch (e) {
                // If surroundContents fails, extract and wrap the content
                const content = range.extractContents();
                headingElement.appendChild(content);
                range.insertNode(headingElement);
            }
        } else {
            // Replace the existing block element with a heading
            const headingElement = document.createElement(headingTag);
            headingElement.innerHTML = blockElement.innerHTML;

            // Preserve cursor position
            const offset = range.startOffset;
            const textNode = range.startContainer;

            blockElement.parentNode?.replaceChild(headingElement, blockElement);

            // Restore cursor position
            try {
                const newRange = document.createRange();
                if (textNode.nodeType === Node.TEXT_NODE && headingElement.contains(textNode)) {
                    newRange.setStart(textNode, offset);
                    newRange.setEnd(textNode, offset);
                } else {
                    // Fallback: place cursor at the end of the heading
                    newRange.selectNodeContents(headingElement);
                    newRange.collapse(false);
                }
                selection.removeAllRanges();
                selection.addRange(newRange);
            } catch (e) {
                // Fallback: just focus the editor
                editorRef.current.focus();
            }
        }

        handleInput();
    };

    return (
        <div className={cn("rich-text-editor border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm", error && "border-red-500", className)}>
            {/* Toolbar */}
            <div className="border-b border-gray-200 bg-gray-50 px-3 py-2">
                <div className="flex flex-wrap items-center gap-1">
                    {/* Text Formatting Group */}
                    <div className="flex items-center gap-1 mr-3">
                        <button
                            type="button"
                            onClick={() => formatText('bold')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Bold (Ctrl+B)"
                        >
                            <Bold className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('italic')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Italic (Ctrl+I)"
                        >
                            <Italic className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('underline')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Underline (Ctrl+U)"
                        >
                            <Underline className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Separator */}
                    <div className="w-px h-6 bg-gray-300 mx-2"></div>

                    {/* Headings Group */}
                    <div className="flex items-center gap-1 mr-3">
                        <button
                            type="button"
                            onClick={() => applyHeading('h1')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Heading 1"
                        >
                            <Heading1 className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => applyHeading('h2')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Heading 2"
                        >
                            <Heading2 className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => applyHeading('h3')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Heading 3"
                        >
                            <Heading3 className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Separator */}
                    <div className="w-px h-6 bg-gray-300 mx-2"></div>

                    {/* Lists Group */}
                    <div className="flex items-center gap-1 mr-3">
                        <button
                            type="button"
                            onClick={() => insertList(false)}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Bullet List"
                        >
                            <List className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => insertList(true)}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Numbered List"
                        >
                            <ListOrdered className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Separator */}
                    <div className="w-px h-6 bg-gray-300 mx-2"></div>

                    {/* Alignment Group */}
                    <div className="flex items-center gap-1 mr-3">
                        <button
                            type="button"
                            onClick={() => formatText('justifyLeft')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Align Left"
                        >
                            <AlignLeft className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('justifyCenter')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Align Center"
                        >
                            <AlignCenter className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('justifyRight')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Align Right"
                        >
                            <AlignRight className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Separator */}
                    <div className="w-px h-6 bg-gray-300 mx-2"></div>

                    {/* Links Group */}
                    <div className="flex items-center gap-1">
                        <button
                            type="button"
                            onClick={() => formatText('createLink', prompt('Enter URL:') || '')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Insert Link"
                        >
                            <Link className="w-4 h-4" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('unlink')}
                            disabled={disabled}
                            className="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Remove Link"
                        >
                            <Unlink className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>

            {/* Editor */}
            <div
                ref={editorRef}
                contentEditable={!disabled}
                onInput={handleInput}
                onKeyDown={handleKeyDown}
                className={cn(
                    "min-h-[280px] p-4 outline-none bg-white text-gray-900 leading-relaxed",
                    "prose prose-sm max-w-none",
                    "focus:ring-2 focus:ring-[#20B2AA] focus:ring-opacity-20",
                    disabled && "bg-gray-50 cursor-not-allowed opacity-60"
                )}
                style={{
                    wordBreak: 'break-word',
                    overflowWrap: 'break-word',
                    fontSize: '14px',
                    lineHeight: '1.6'
                }}
                data-placeholder={placeholder}
                suppressContentEditableWarning={true}
            />

            {/* Custom styles for headings in the editor */}
            <style dangerouslySetInnerHTML={{
                __html: `
                    .rich-text-editor [contenteditable] h1 {
                        font-size: 2em !important;
                        font-weight: bold !important;
                        margin: 0.67em 0 !important;
                        line-height: 1.2 !important;
                        color: #1f2937 !important;
                    }
                    .rich-text-editor [contenteditable] h2 {
                        font-size: 1.5em !important;
                        font-weight: bold !important;
                        margin: 0.75em 0 !important;
                        line-height: 1.3 !important;
                        color: #1f2937 !important;
                    }
                    .rich-text-editor [contenteditable] h3 {
                        font-size: 1.17em !important;
                        font-weight: bold !important;
                        margin: 0.83em 0 !important;
                        line-height: 1.4 !important;
                        color: #1f2937 !important;
                    }
                    .rich-text-editor [contenteditable] p {
                        margin: 1em 0 !important;
                    }
                    .rich-text-editor [contenteditable] ul,
                    .rich-text-editor [contenteditable] ol {
                        margin: 1em 0 !important;
                        padding-left: 2em !important;
                    }
                `
            }} />

            {error && (
                <div className="px-4 py-2 bg-red-50 border-t border-red-200">
                    <p className="text-red-600 text-sm">{error}</p>
                </div>
            )}
        </div>
    );
}
