import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Testimonial {
    id: number;
    name: string;
    position: string;
    company: string;
    quote: string;
    rating: number;
    image?: string | null;
}

interface TestimonialCarouselProps {
    testimonials: Testimonial[];
    autoPlay?: boolean;
    autoPlayInterval?: number;
    className?: string;
}

export function TestimonialCarousel({ 
    testimonials, 
    autoPlay = true, 
    autoPlayInterval = 5000,
    className 
}: TestimonialCarouselProps) {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isHovered, setIsHovered] = useState(false);

    // Auto-play functionality
    useEffect(() => {
        if (!autoPlay || isHovered || testimonials.length <= 1) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => 
                prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
            );
        }, autoPlayInterval);

        return () => clearInterval(interval);
    }, [autoPlay, autoPlayInterval, isHovered, testimonials.length]);

    const goToPrevious = () => {
        setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
    };

    const goToNext = () => {
        setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
    };

    const goToSlide = (index: number) => {
        setCurrentIndex(index);
    };

    if (!testimonials || testimonials.length === 0) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-500">No testimonials available</p>
            </div>
        );
    }

    const currentTestimonial = testimonials[currentIndex];

    return (
        <div 
            className={cn("relative w-full", className)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Main Carousel */}
            <div className="relative overflow-hidden rounded-2xl">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentIndex}
                        initial={{ opacity: 0, x: 100 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -100 }}
                        transition={{ duration: 0.5, ease: "easeInOut" }}
                        className="bg-white dark:bg-gray-900 rounded-2xl p-6 sm:p-8 shadow-lg border border-gray-100 dark:border-gray-800 relative overflow-hidden group"
                    >
                        {/* Decorative elements */}
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#20B2AA] to-purple-500"></div>
                        
                        <motion.div 
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                            className="mb-4 sm:mb-6"
                        >
                            <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#20B2AA] opacity-80" fill="currentColor" viewBox="0 0 32 32">
                                <path d="M10 8c-3.314 0-6 2.686-6 6v10h6v-4c0-2.21 1.79-4 4-4V8h-4zm12 0c-3.314 0-6 2.686-6 6v10h6v-4c0-2.21 1.79-4 4-4V8h-4z"/>
                            </svg>
                        </motion.div>

                        {/* Rating Stars */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="flex items-center gap-1 mb-4"
                        >
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Star 
                                    key={i}
                                    className={cn(
                                        "w-4 h-4",
                                        i < currentTestimonial.rating 
                                            ? "text-amber-400 fill-current" 
                                            : "text-gray-300"
                                    )}
                                />
                            ))}
                        </motion.div>

                        {/* Quote */}
                        <motion.blockquote
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-gray-700 dark:text-gray-300 text-base sm:text-lg leading-relaxed mb-6 italic"
                        >
                            "{currentTestimonial.quote}"
                        </motion.blockquote>

                        {/* Author */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                            className="flex items-center gap-4"
                        >
                            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#20B2AA] to-purple-500 flex items-center justify-center text-white text-lg font-semibold">
                                {currentTestimonial.name.charAt(0)}
                            </div>
                            <div>
                                <div className="font-semibold text-base sm:text-lg mb-1 dark:text-white">
                                    {currentTestimonial.name}
                                </div>
                                <div className="text-[#20B2AA] text-xs sm:text-sm">
                                    {currentTestimonial.position} at {currentTestimonial.company}
                                </div>
                            </div>
                        </motion.div>

                        {/* Hover effect gradient */}
                        <div className="absolute inset-0 bg-gradient-to-br from-[#20B2AA]/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                    </motion.div>
                </AnimatePresence>
            </div>

            {/* Navigation Buttons */}
            {testimonials.length > 1 && (
                <>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={goToPrevious}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-gray-200 z-10"
                    >
                        <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={goToNext}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-gray-200 z-10"
                    >
                        <ChevronRight className="w-4 h-4" />
                    </Button>
                </>
            )}

            {/* Dots Indicator */}
            {testimonials.length > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                    {testimonials.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={cn(
                                "w-2 h-2 rounded-full transition-all duration-300",
                                index === currentIndex
                                    ? "bg-[#20B2AA] w-8"
                                    : "bg-gray-300 hover:bg-gray-400"
                            )}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
