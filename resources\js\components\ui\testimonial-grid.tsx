import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Testimonial {
    id: number;
    name: string;
    position: string;
    company: string;
    quote: string;
    rating: number;
    image?: string | null;
}

interface TestimonialGridProps {
    testimonials: Testimonial[];
    autoPlay?: boolean;
    autoPlayInterval?: number;
    className?: string;
}

export function TestimonialGrid({ 
    testimonials, 
    autoPlay = true, 
    autoPlayInterval = 5000,
    className 
}: TestimonialGridProps) {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isHovered, setIsHovered] = useState(false);

    // Calculate items per page based on screen size
    const getItemsPerPage = () => {
        if (typeof window !== 'undefined') {
            return window.innerWidth >= 768 ? 3 : 1; // 3 on desktop, 1 on mobile
        }
        return 3;
    };

    const [itemsPerPage, setItemsPerPage] = useState(getItemsPerPage);

    // Handle window resize
    useEffect(() => {
        const handleResize = () => {
            setItemsPerPage(getItemsPerPage());
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Calculate total pages
    const totalPages = Math.ceil(testimonials.length / itemsPerPage);

    // Auto-play functionality
    useEffect(() => {
        if (!autoPlay || isHovered || totalPages <= 1) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => 
                prevIndex === totalPages - 1 ? 0 : prevIndex + 1
            );
        }, autoPlayInterval);

        return () => clearInterval(interval);
    }, [autoPlay, autoPlayInterval, isHovered, totalPages]);

    const goToPrevious = () => {
        setCurrentIndex(currentIndex === 0 ? totalPages - 1 : currentIndex - 1);
    };

    const goToNext = () => {
        setCurrentIndex(currentIndex === totalPages - 1 ? 0 : currentIndex + 1);
    };

    const goToSlide = (index: number) => {
        setCurrentIndex(index);
    };

    // Get current testimonials to display
    const getCurrentTestimonials = () => {
        const startIndex = currentIndex * itemsPerPage;
        return testimonials.slice(startIndex, startIndex + itemsPerPage);
    };

    const currentTestimonials = getCurrentTestimonials();

    if (!testimonials || testimonials.length === 0) {
        return null;
    }

    return (
        <div 
            className={cn("relative w-full", className)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Main Grid */}
            <div className="relative overflow-hidden">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentIndex}
                        initial={{ opacity: 0, x: 100 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -100 }}
                        transition={{ duration: 0.5, ease: "easeInOut" }}
                        className={cn(
                            "grid gap-6",
                            itemsPerPage === 1 ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
                        )}
                    >
                        {currentTestimonials.map((testimonial, index) => (
                            <motion.div
                                key={testimonial.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                className="bg-white dark:bg-gray-900 rounded-2xl p-6 shadow-lg border border-gray-100 dark:border-gray-800 relative overflow-hidden group"
                            >
                                {/* Decorative elements */}
                                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#20B2AA] to-purple-500"></div>

                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                                    className="mb-4"
                                >
                                    <svg className="w-8 h-8 text-[#20B2AA] opacity-80" fill="currentColor" viewBox="0 0 32 32">
                                        <path d="M10 8c-3.314 0-6 2.686-6 6v10h6v-4c0-2.21 1.79-4 4-4V8h-4zm12 0c-3.314 0-6 2.686-6 6v10h6v-4c0-2.21 1.79-4 4-4V8h-4z"/>
                                    </svg>
                                </motion.div>

                                {/* Rating Stars */}
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                                    className="flex items-center gap-1 mb-4"
                                >
                                    {Array.from({ length: 5 }).map((_, i) => (
                                        <Star
                                            key={i}
                                            className={cn(
                                                "w-4 h-4",
                                                i < testimonial.rating
                                                    ? "text-amber-400 fill-current"
                                                    : "text-gray-300"
                                            )}
                                        />
                                    ))}
                                </motion.div>

                                {/* Quote */}
                                <motion.blockquote
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                                    className="text-gray-700 dark:text-gray-300 mb-6 text-sm sm:text-base leading-relaxed italic"
                                >
                                    "{testimonial.quote}"
                                </motion.blockquote>

                                {/* Author */}
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                                    className="flex items-center gap-3"
                                >
                                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#20B2AA] to-purple-500 flex items-center justify-center text-white text-sm font-semibold">
                                        {testimonial.name.charAt(0)}
                                    </div>
                                    <div>
                                        <div className="font-semibold text-gray-900 dark:text-white text-sm">
                                            {testimonial.name}
                                        </div>
                                        <div className="text-xs text-gray-600 dark:text-gray-400">
                                            {testimonial.position} at {testimonial.company}
                                        </div>
                                    </div>
                                </motion.div>

                                {/* Hover effect gradient */}
                                <div className="absolute inset-0 bg-gradient-to-br from-[#20B2AA]/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                            </motion.div>
                        ))}
                    </motion.div>
                </AnimatePresence>
            </div>

            {/* Navigation Buttons */}
            {totalPages > 1 && (
                <>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={goToPrevious}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-gray-200 z-10"
                    >
                        <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={goToNext}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-gray-200 z-10"
                    >
                        <ChevronRight className="w-4 h-4" />
                    </Button>
                </>
            )}

            {/* Dots Indicator */}
            {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                    {Array.from({ length: totalPages }).map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={cn(
                                "w-2 h-2 rounded-full transition-all duration-300",
                                index === currentIndex
                                    ? "bg-[#20B2AA] w-8"
                                    : "bg-gray-300 hover:bg-gray-400"
                            )}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
