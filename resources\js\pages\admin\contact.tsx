import { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { router } from '@inertiajs/react';
import { Mail, Phone, MapPin, Eye, Archive, Reply, Trash2, RotateCcw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface ContactManagement {
    id: number;
    section_badge: string;
    section_title: string;
    section_description: string;
    email: string;
    phone: string;
    location: string;
    form_name_label: string;
    form_name_placeholder: string;
    form_email_label: string;
    form_email_placeholder: string;
    form_subject_label: string;
    form_subject_placeholder: string;
    form_message_label: string;
    form_message_placeholder: string;
    form_submit_button_text: string;
    email_label: string;
    email_subtitle: string;
    phone_label: string;
    phone_subtitle: string;
    location_label: string;
    location_subtitle: string;
    success_message: string;
    error_message: string;
}

interface ContactMessage {
    id: number;
    from: string;
    email: string;
    subject: string;
    date: string;
    status: 'unread' | 'read' | 'replied' | 'archived';
    content?: string;
}

interface Props {
    contactManagement: ContactManagement;
    messages: ContactMessage[];
    counts: {
        all: number;
        unread: number;
        read: number;
        replied: number;
        archived: number;
    };
}

export default function Contact({ contactManagement, messages, counts }: Props) {
    const [messageTab, setMessageTab] = useState<'all' | 'unread' | 'read' | 'replied' | 'archived'>('all');
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        section_badge: contactManagement.section_badge || '',
        section_title: contactManagement.section_title || '',
        section_description: contactManagement.section_description || '',
        email: contactManagement.email || '',
        phone: contactManagement.phone || '',
        location: contactManagement.location || '',
        form_name_label: contactManagement.form_name_label || '',
        form_name_placeholder: contactManagement.form_name_placeholder || '',
        form_email_label: contactManagement.form_email_label || '',
        form_email_placeholder: contactManagement.form_email_placeholder || '',
        form_subject_label: contactManagement.form_subject_label || '',
        form_subject_placeholder: contactManagement.form_subject_placeholder || '',
        form_message_label: contactManagement.form_message_label || '',
        form_message_placeholder: contactManagement.form_message_placeholder || '',
        form_submit_button_text: contactManagement.form_submit_button_text || '',
        email_label: contactManagement.email_label || '',
        email_subtitle: contactManagement.email_subtitle || '',
        phone_label: contactManagement.phone_label || '',
        phone_subtitle: contactManagement.phone_subtitle || '',
        location_label: contactManagement.location_label || '',
        location_subtitle: contactManagement.location_subtitle || '',
        success_message: contactManagement.success_message || '',
        error_message: contactManagement.error_message || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        put(route('admin.contact.management.update'), {
            onSuccess: () => {
                toast.success('Contact settings updated successfully');
                router.reload();
            },
            onError: (errors) => {
                console.error('Contact update errors:', errors);
                toast.error('Failed to update contact settings. Please check the form for errors.');
            },
        });
    };

    const filteredMessages = messages.filter(message => {
        const matchesSearch = 
            message.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
            message.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
            message.subject.toLowerCase().includes(searchQuery.toLowerCase());
        
        if (messageTab === 'all') return matchesSearch;
        return matchesSearch && message.status === messageTab;
    });

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'unread': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'read': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'replied': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
            case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    return (
        <AdminLayout>
            <Head title="Contact Management" />

            <div className="p-6 space-y-6 bg-white min-h-screen">
                {/* Header */}
                <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">Contact Management</h1>
                            <p className="text-sm text-gray-500 mt-1">Manage your contact section content and view messages from visitors</p>
                        </div>
                    </div>
                </div>

                {/* Tabs */}
                <Tabs defaultValue="messages" className="w-full">
                    <TabsList className="mb-6 border border-gray-200">
                        <TabsTrigger
                            value="messages"
                            className="border-r border-gray-200"
                        >
                            Messages ({counts.all})
                        </TabsTrigger>
                        <TabsTrigger value="management">Contact Management</TabsTrigger>
                    </TabsList>

                    {/* Messages Tab */}
                    <TabsContent value="messages">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Messages</CardTitle>
                                    <CardDescription>
                                        View and manage messages from your contact form.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {/* Message Filters */}
                                    <div className="flex flex-wrap gap-2 mb-6">
                                        {[
                                            { key: 'all', label: 'All', count: counts.all },
                                            { key: 'unread', label: 'Unread', count: counts.unread },
                                            { key: 'read', label: 'Read', count: counts.read },
                                            { key: 'replied', label: 'Replied', count: counts.replied },
                                            { key: 'archived', label: 'Archived', count: counts.archived },
                                        ].map((filter) => (
                                            <button
                                                key={filter.key}
                                                onClick={() => setMessageTab(filter.key as any)}
                                                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                                                    messageTab === filter.key
                                                        ? 'bg-primary text-primary-foreground'
                                                        : 'bg-muted text-muted-foreground hover:bg-muted/80'
                                                }`}
                                            >
                                                {filter.label} ({filter.count})
                                            </button>
                                        ))}
                                    </div>

                                    {/* Search */}
                                    <div className="mb-6">
                                        <Input
                                            placeholder="Search messages..."
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="max-w-sm"
                                        />
                                    </div>

                                    {/* Messages List */}
                                    <div className="space-y-4">
                                        {filteredMessages.length === 0 ? (
                                            <div className="text-center py-8 text-muted-foreground">
                                                No messages found.
                                            </div>
                                        ) : (
                                            filteredMessages.map((message) => (
                                                <div
                                                    key={message.id}
                                                    className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                                                >
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex-1">
                                                            <div className="flex items-center gap-3 mb-2">
                                                                <h4 className="font-medium">{message.from}</h4>
                                                                <Badge className={getStatusColor(message.status)}>
                                                                    {message.status}
                                                                </Badge>
                                                            </div>
                                                            <p className="text-sm text-muted-foreground mb-1">
                                                                {message.email}
                                                            </p>
                                                            <p className="font-medium mb-2">{message.subject}</p>
                                                            <p className="text-sm text-muted-foreground">{message.date}</p>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Button variant="ghost" size="sm">
                                                                <Eye className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="ghost" size="sm">
                                                                <Reply className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="ghost" size="sm">
                                                                <Archive className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="ghost" size="sm">
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                    </TabsContent>

                    {/* Contact Management Tab */}
                    <TabsContent value="management">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Section Content Card */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Section Content</CardTitle>
                                    <CardDescription>
                                        Configure your contact section badge, title, and description.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="section_badge">Section Badge</Label>
                                            <Input
                                                id="section_badge"
                                                value={data.section_badge}
                                                onChange={(e) => setData('section_badge', e.target.value)}
                                                placeholder="Contact"
                                            />
                                            {errors.section_badge && (
                                                <p className="text-sm text-red-600 mt-1">{errors.section_badge}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="section_title">Section Title</Label>
                                            <Input
                                                id="section_title"
                                                value={data.section_title}
                                                onChange={(e) => setData('section_title', e.target.value)}
                                                placeholder="Get In Touch"
                                            />
                                            {errors.section_title && (
                                                <p className="text-sm text-red-600 mt-1">{errors.section_title}</p>
                                            )}
                                        </div>
                                    </div>
                                    <div>
                                        <Label htmlFor="section_description">Section Description</Label>
                                        <Textarea
                                            id="section_description"
                                            value={data.section_description}
                                            onChange={(e) => setData('section_description', e.target.value)}
                                            placeholder="Have a project in mind? Let's discuss your ideas and bring them to life."
                                            rows={3}
                                        />
                                        {errors.section_description && (
                                            <p className="text-sm text-red-600 mt-1">{errors.section_description}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Contact Information Card */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Information</CardTitle>
                                    <CardDescription>
                                        Configure your contact details displayed on the contact section.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <Label htmlFor="email">Email Address</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                placeholder="<EMAIL>"
                                            />
                                            {errors.email && (
                                                <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="+****************"
                                            />
                                            {errors.phone && (
                                                <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="location">Location</Label>
                                            <Input
                                                id="location"
                                                value={data.location}
                                                onChange={(e) => setData('location', e.target.value)}
                                                placeholder="City, Country"
                                            />
                                            {errors.location && (
                                                <p className="text-sm text-red-600 mt-1">{errors.location}</p>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Contact Form Settings Card */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Form Settings</CardTitle>
                                    <CardDescription>
                                        Configure form field labels and placeholders for the contact form.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="form_name_label">Name Field Label</Label>
                                            <Input
                                                id="form_name_label"
                                                value={data.form_name_label}
                                                onChange={(e) => setData('form_name_label', e.target.value)}
                                                placeholder="Name"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_name_placeholder">Name Field Placeholder</Label>
                                            <Input
                                                id="form_name_placeholder"
                                                value={data.form_name_placeholder}
                                                onChange={(e) => setData('form_name_placeholder', e.target.value)}
                                                placeholder="Your Name"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_email_label">Email Field Label</Label>
                                            <Input
                                                id="form_email_label"
                                                value={data.form_email_label}
                                                onChange={(e) => setData('form_email_label', e.target.value)}
                                                placeholder="Email"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_email_placeholder">Email Field Placeholder</Label>
                                            <Input
                                                id="form_email_placeholder"
                                                value={data.form_email_placeholder}
                                                onChange={(e) => setData('form_email_placeholder', e.target.value)}
                                                placeholder="<EMAIL>"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_subject_label">Subject Field Label</Label>
                                            <Input
                                                id="form_subject_label"
                                                value={data.form_subject_label}
                                                onChange={(e) => setData('form_subject_label', e.target.value)}
                                                placeholder="Subject"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_subject_placeholder">Subject Field Placeholder</Label>
                                            <Input
                                                id="form_subject_placeholder"
                                                value={data.form_subject_placeholder}
                                                onChange={(e) => setData('form_subject_placeholder', e.target.value)}
                                                placeholder="Project Subject"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_message_label">Message Field Label</Label>
                                            <Input
                                                id="form_message_label"
                                                value={data.form_message_label}
                                                onChange={(e) => setData('form_message_label', e.target.value)}
                                                placeholder="Message"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="form_message_placeholder">Message Field Placeholder</Label>
                                            <Input
                                                id="form_message_placeholder"
                                                value={data.form_message_placeholder}
                                                onChange={(e) => setData('form_message_placeholder', e.target.value)}
                                                placeholder="Your Message"
                                            />
                                        </div>
                                        <div className="md:col-span-2">
                                            <Label htmlFor="form_submit_button_text">Submit Button Text</Label>
                                            <Input
                                                id="form_submit_button_text"
                                                value={data.form_submit_button_text}
                                                onChange={(e) => setData('form_submit_button_text', e.target.value)}
                                                placeholder="Send Message"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Contact Info Labels Card */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Info Labels</CardTitle>
                                    <CardDescription>
                                        Configure display labels and subtitles for contact information.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="email_label">Email Label</Label>
                                            <Input
                                                id="email_label"
                                                value={data.email_label}
                                                onChange={(e) => setData('email_label', e.target.value)}
                                                placeholder="Email"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="email_subtitle">Email Subtitle</Label>
                                            <Input
                                                id="email_subtitle"
                                                value={data.email_subtitle}
                                                onChange={(e) => setData('email_subtitle', e.target.value)}
                                                placeholder="For general inquiries:"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="phone_label">Phone Label</Label>
                                            <Input
                                                id="phone_label"
                                                value={data.phone_label}
                                                onChange={(e) => setData('phone_label', e.target.value)}
                                                placeholder="Phone"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="phone_subtitle">Phone Subtitle</Label>
                                            <Input
                                                id="phone_subtitle"
                                                value={data.phone_subtitle}
                                                onChange={(e) => setData('phone_subtitle', e.target.value)}
                                                placeholder="Available Monday-Friday:"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="location_label">Location Label</Label>
                                            <Input
                                                id="location_label"
                                                value={data.location_label}
                                                onChange={(e) => setData('location_label', e.target.value)}
                                                placeholder="Location"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="location_subtitle">Location Subtitle</Label>
                                            <Input
                                                id="location_subtitle"
                                                value={data.location_subtitle}
                                                onChange={(e) => setData('location_subtitle', e.target.value)}
                                                placeholder="Based in:"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Form Messages Card */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Form Messages</CardTitle>
                                    <CardDescription>
                                        Configure success and error messages for the contact form.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4">
                                        <div>
                                            <Label htmlFor="success_message">Success Message</Label>
                                            <Textarea
                                                id="success_message"
                                                value={data.success_message}
                                                onChange={(e) => setData('success_message', e.target.value)}
                                                placeholder="Your message has been sent successfully. I will get back to you soon!"
                                                rows={2}
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="error_message">Error Message</Label>
                                            <Textarea
                                                id="error_message"
                                                value={data.error_message}
                                                onChange={(e) => setData('error_message', e.target.value)}
                                                placeholder="Sorry, there was an error sending your message. Please try again."
                                                rows={2}
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Save Button */}
                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing} className="bg-teal-500 hover:bg-teal-600 text-white">
                                    {processing ? 'Saving...' : 'Save Settings'}
                                </Button>
                            </div>
                        </form>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
