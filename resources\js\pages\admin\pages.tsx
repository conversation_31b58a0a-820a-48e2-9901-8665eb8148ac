import { Head, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit, Eye, Trash2, ToggleLeft, ToggleRight, Star, StarOff } from 'lucide-react';
import { toast } from 'sonner';

interface Page {
    id: number;
    title: string;
    slug: string;
    status: 'draft' | 'published';
    is_featured: boolean;
    published_at: string | null;
    created_at: string;
    excerpt: string;
    featured_image: string | null;
    url: string;
}

interface Props {
    pages: Page[];
}

export default function Pages({ pages }: Props) {
    const handleToggleStatus = (page: Page) => {
        router.post(route('admin.pages.toggle-status', page.id), {}, {
            onSuccess: () => {
                toast.success(`Page ${page.status === 'published' ? 'unpublished' : 'published'} successfully`);
            },
            onError: () => {
                toast.error('Failed to update page status');
            },
        });
    };

    const handleToggleFeatured = (page: Page) => {
        router.post(route('admin.pages.toggle-featured', page.id), {}, {
            onSuccess: () => {
                toast.success(`Page ${page.is_featured ? 'unfeatured' : 'featured'} successfully`);
            },
            onError: () => {
                toast.error('Failed to update featured status');
            },
        });
    };

    const handleDelete = (page: Page) => {
        if (confirm(`Are you sure you want to delete "${page.title}"? This action cannot be undone.`)) {
            router.delete(route('admin.pages.destroy', page.id), {
                onSuccess: () => {
                    toast.success('Page deleted successfully');
                },
                onError: () => {
                    toast.error('Failed to delete page');
                },
            });
        }
    };

    return (
        <AdminLayout>
            <Head title="Page Management - Portfolio Admin" />

            {/* Dashboard Header */}
            <div className="border-b border-gray-200 bg-white">
                <div className="px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">Page Management</h1>
                            <p className="mt-1 text-sm text-gray-500">Create and manage your website pages</p>
                        </div>
                        <Link href={route('admin.pages.create')}>
                            <Button className="bg-[#20B2AA] hover:bg-[#1a9994] text-white">
                                <Plus className="w-4 h-4 mr-2" />
                                Create Page
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>

            <div className="p-6 space-y-6">
                {/* Content */}
                <Card className="p-6">
                    <Tabs defaultValue="pages" className="w-full">
                        <TabsList className="mb-4">
                            <TabsTrigger value="pages">Pages Management</TabsTrigger>
                            <TabsTrigger value="settings">Page Settings</TabsTrigger>
                        </TabsList>

                        <TabsContent value="pages">
                            <div className="space-y-6">
                                <div className="flex justify-between items-center">
                                    <div>
                                        <h2 className="text-lg font-medium text-gray-900">Website Pages</h2>
                                        <p className="text-sm text-gray-500">Manage your website pages and content</p>
                                    </div>
                                </div>

                                {pages.length === 0 ? (
                                    <div className="text-center py-12">
                                        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                            <Plus className="w-8 h-8 text-gray-400" />
                                        </div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No pages yet</h3>
                                        <p className="text-gray-600 mb-4">Get started by creating your first page</p>
                                        <Link href={route('admin.pages.create')}>
                                            <Button className="bg-[#20B2AA] hover:bg-[#1a9994] text-white">
                                                <Plus className="w-4 h-4 mr-2" />
                                                Create Page
                                            </Button>
                                        </Link>
                                    </div>
                                ) : (
                                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                        <div className="overflow-x-auto">
                                            <table className="w-full">
                                                <thead className="bg-gray-50">
                                                    <tr>
                                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Page
                                                        </th>
                                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Status
                                                        </th>
                                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Published
                                                        </th>
                                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Actions
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="bg-white divide-y divide-gray-200">
                                                    {pages.map((page) => (
                                                        <tr key={page.id} className="hover:bg-gray-50">
                                                            <td className="px-6 py-4">
                                                                <div className="flex items-center">
                                                                    {page.featured_image && (
                                                                        <img
                                                                            src={page.featured_image}
                                                                            alt={page.title}
                                                                            className="w-12 h-12 rounded-lg object-cover mr-4"
                                                                        />
                                                                    )}
                                                                    <div>
                                                                        <div className="flex items-center gap-2">
                                                                            <h3 className="text-sm font-medium text-gray-900">
                                                                                {page.title}
                                                                            </h3>
                                                                            {page.is_featured && (
                                                                                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                                                            )}
                                                                        </div>
                                                                        <p className="text-sm text-gray-500">
                                                                            /{page.slug}
                                                                        </p>
                                                                        <p className="text-sm text-gray-600 mt-1">
                                                                            {page.excerpt}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td className="px-6 py-4">
                                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                                    page.status === 'published'
                                                                        ? 'bg-green-100 text-green-800'
                                                                        : 'bg-yellow-100 text-yellow-800'
                                                                }`}>
                                                                    {page.status}
                                                                </span>
                                                            </td>
                                                            <td className="px-6 py-4 text-sm text-gray-500">
                                                                {page.published_at || 'Not published'}
                                                            </td>
                                                            <td className="px-6 py-4">
                                                                <div className="flex items-center gap-2">
                                                                    <button
                                                                        onClick={() => handleToggleStatus(page)}
                                                                        className="text-gray-400 hover:text-gray-600"
                                                                        title={`${page.status === 'published' ? 'Unpublish' : 'Publish'} page`}
                                                                    >
                                                                        {page.status === 'published' ? (
                                                                            <ToggleRight className="w-5 h-5 text-green-500" />
                                                                        ) : (
                                                                            <ToggleLeft className="w-5 h-5" />
                                                                        )}
                                                                    </button>
                                                                    <button
                                                                        onClick={() => handleToggleFeatured(page)}
                                                                        className="text-gray-400 hover:text-gray-600"
                                                                        title={`${page.is_featured ? 'Remove from' : 'Add to'} featured`}
                                                                    >
                                                                        {page.is_featured ? (
                                                                            <Star className="w-5 h-5 text-yellow-500 fill-current" />
                                                                        ) : (
                                                                            <StarOff className="w-5 h-5" />
                                                                        )}
                                                                    </button>
                                                                    <a
                                                                        href={page.url}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        className="text-gray-400 hover:text-gray-600"
                                                                        title="View page"
                                                                    >
                                                                        <Eye className="w-5 h-5" />
                                                                    </a>
                                                                    <Link
                                                                        href={route('admin.pages.edit', page.id)}
                                                                        className="text-gray-400 hover:text-gray-600"
                                                                        title="Edit page"
                                                                    >
                                                                        <Edit className="w-5 h-5" />
                                                                    </Link>
                                                                    <button
                                                                        onClick={() => handleDelete(page)}
                                                                        className="text-gray-400 hover:text-red-600"
                                                                        title="Delete page"
                                                                    >
                                                                        <Trash2 className="w-5 h-5" />
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="settings">
                            <div className="space-y-6">
                                <div className="flex justify-between items-center">
                                    <div>
                                        <h2 className="text-lg font-medium text-gray-900">Page Settings</h2>
                                        <p className="text-sm text-gray-500">Configure global page settings and defaults</p>
                                    </div>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-6">
                                    <p className="text-sm text-gray-600">Page settings will be available in future updates.</p>
                                </div>
                            </div>
                        </TabsContent>
                    </Tabs>
                </Card>
            </div>
        </AdminLayout>
    );
}
