import { Head, useForm, Link } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { useState } from 'react';

export default function CreatePage() {
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const [isSlugManuallyEdited, setIsSlugManuallyEdited] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        title: '',
        slug: '',
        content: '',
        meta_description: '',
        meta_keywords: '',
        page_layout: 'default',
        status: 'draft',
        publish_immediately: false,
        is_featured: false,
        featured_image: null as File | null,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                if (key === 'featured_image' && value instanceof File) {
                    formData.append(key, value);
                } else if (typeof value === 'boolean') {
                    formData.append(key, value ? '1' : '0');
                } else {
                    formData.append(key, String(value));
                }
            }
        });

        post(route('admin.pages.store'), {
            data: formData,
            forceFormData: true,
            onSuccess: () => {
                toast.success('Page created successfully!');
            },
            onError: (errors) => {
                console.error('Form errors:', errors);
                toast.error('Please check the form for errors');
            },
        });
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('featured_image', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreviewImage(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    };

    const handleTitleChange = (title: string) => {
        setData('title', title);
        // Auto-generate slug only if it hasn't been manually edited
        if (!isSlugManuallyEdited) {
            setData('slug', generateSlug(title));
        }
    };

    const handleSlugChange = (slug: string) => {
        setData('slug', slug);
        // Mark slug as manually edited if user types in it
        if (slug.length > 0) {
            setIsSlugManuallyEdited(true);
        } else {
            // If user clears the slug, allow auto-generation again
            setIsSlugManuallyEdited(false);
        }
    };

    return (
        <AdminLayout>
            <Head title="Create Page" />
            
            {/* Dashboard Header */}
            <div className="border-b border-gray-200 bg-white">
                <div className="px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.pages.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Create Page</h1>
                                <p className="mt-1 text-sm text-gray-500">Create a new page with rich content and SEO settings</p>
                            </div>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setData('status', 'draft')}
                                disabled={processing}
                            >
                                <Save className="w-4 h-4 mr-2" />
                                Save as Draft
                            </Button>
                            <Button
                                type="submit"
                                form="page-form"
                                className="bg-[#20B2AA] hover:bg-[#1a9994] text-white"
                                disabled={processing}
                                onClick={() => setData('status', 'published')}
                            >
                                <Eye className="w-4 h-4 mr-2" />
                                Create Page
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <div className="p-6 space-y-6">

                <form id="page-form" onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Page Content Card */}
                        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-[#20B2AA] rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900">Page Content</h2>
                            </div>
                            <p className="text-sm text-gray-600 mb-6">
                                Enter the main content and details for your page
                            </p>

                            <div className="space-y-4">
                                {/* Title */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Title *
                                    </label>
                                    <input
                                        type="text"
                                        value={data.title}
                                        onChange={(e) => handleTitleChange(e.target.value)}
                                        placeholder="Enter page title"
                                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                            errors.title ? 'border-red-500' : 'border-gray-300'
                                        } bg-white text-gray-900`}
                                        required
                                    />
                                    {errors.title && (
                                        <p className="text-red-500 text-sm mt-1">{errors.title}</p>
                                    )}
                                </div>

                                {/* URL Slug */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        URL Slug *
                                    </label>
                                    <div className="flex">
                                        <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                            /page/
                                        </span>
                                        <input
                                            type="text"
                                            value={data.slug}
                                            onChange={(e) => handleSlugChange(e.target.value)}
                                            placeholder="page-url-slug"
                                            className={`flex-1 px-3 py-2 border rounded-r-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                                errors.slug ? 'border-red-500' : 'border-gray-300'
                                            } bg-white text-gray-900`}
                                            required
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        URL-friendly version of the title. Leave blank to auto-generate.
                                    </p>
                                    {errors.slug && (
                                        <p className="text-red-500 text-sm mt-1">{errors.slug}</p>
                                    )}
                                </div>

                                {/* Content */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Content *
                                    </label>
                                    <RichTextEditor
                                        value={data.content}
                                        onChange={(content) => setData('content', content)}
                                        placeholder="Use the toolbar above to format your content with headings, lists, and more..."
                                        error={errors.content}
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Use the toolbar above to format your content with headings, lists, and more.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* SEO Settings Card */}
                        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900">SEO Settings</h2>
                            </div>
                            <p className="text-sm text-gray-600 mb-6">
                                Optimize your page for search engines
                            </p>

                            <div className="space-y-4">
                                {/* Meta Description */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Meta Description
                                    </label>
                                    <textarea
                                        value={data.meta_description}
                                        onChange={(e) => setData('meta_description', e.target.value)}
                                        placeholder="Brief description for search engines (160 characters max)"
                                        rows={3}
                                        maxLength={160}
                                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all resize-none ${
                                            errors.meta_description ? 'border-red-500' : 'border-gray-300'
                                        } bg-white text-gray-900`}
                                    />
                                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>Brief description for search engines (160 characters max)</span>
                                        <span>{data.meta_description.length}/160</span>
                                    </div>
                                    {errors.meta_description && (
                                        <p className="text-red-500 text-sm mt-1">{errors.meta_description}</p>
                                    )}
                                </div>

                                {/* Meta Keywords */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Meta Keywords
                                    </label>
                                    <input
                                        type="text"
                                        value={data.meta_keywords}
                                        onChange={(e) => setData('meta_keywords', e.target.value)}
                                        placeholder="keyword1, keyword2, keyword3"
                                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                            errors.meta_keywords ? 'border-red-500' : 'border-gray-300'
                                        } bg-white text-gray-900`}
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Separate keywords with commas
                                    </p>
                                    {errors.meta_keywords && (
                                        <p className="text-red-500 text-sm mt-1">{errors.meta_keywords}</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Publishing Card */}
                        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900">Publishing</h2>
                            </div>

                            <div className="space-y-4">
                                {/* Publish Immediately */}
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="publish_immediately"
                                        checked={data.publish_immediately}
                                        onChange={(e) => setData('publish_immediately', e.target.checked)}
                                        className="w-4 h-4 text-[#20B2AA] bg-gray-100 border-gray-300 rounded focus:ring-[#20B2AA] focus:ring-2"
                                    />
                                    <label htmlFor="publish_immediately" className="ml-2 text-sm font-medium text-gray-700">
                                        Publish immediately
                                    </label>
                                </div>

                                {/* Page Layout */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Page Layout
                                    </label>
                                    <select
                                        value={data.page_layout}
                                        onChange={(e) => setData('page_layout', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white text-gray-900"
                                    >
                                        <option value="default">Default Layout</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Featured Image Card */}
                        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900">Featured Image</h2>
                            </div>

                            <div className="space-y-4">
                                {previewImage && (
                                    <div className="relative">
                                        <img
                                            src={previewImage}
                                            alt="Preview"
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setPreviewImage(null);
                                                setData('featured_image', null);
                                            }}
                                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                                        >
                                            ×
                                        </button>
                                    </div>
                                )}
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Select Image
                                    </label>
                                    <input
                                        type="file"
                                        accept="image/*"
                                        onChange={handleImageChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white text-gray-900"
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Or enter image URL
                                    </p>
                                    <input
                                        type="url"
                                        placeholder="Select featured image"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white text-gray-900 mt-2"
                                        disabled
                                    />
                                    {errors.featured_image && (
                                        <p className="text-red-500 text-sm mt-1">{errors.featured_image}</p>
                                    )}
                                </div>

                                {/* Featured Toggle */}
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_featured"
                                        checked={data.is_featured}
                                        onChange={(e) => setData('is_featured', e.target.checked)}
                                        className="w-4 h-4 text-[#20B2AA] bg-gray-100 border-gray-300 rounded focus:ring-[#20B2AA] focus:ring-2"
                                    />
                                    <label htmlFor="is_featured" className="ml-2 text-sm font-medium text-gray-700">
                                        Featured page
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
