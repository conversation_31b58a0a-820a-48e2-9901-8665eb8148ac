import { Head, useForm, Link, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { <PERSON><PERSON>ef<PERSON>, Save, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { useState } from 'react';

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string;
    meta_description: string | null;
    meta_keywords: string | null;
    featured_image: string | null;
    page_layout: string;
    status: 'draft' | 'published';
    publish_immediately: boolean;
    is_featured: boolean;
    published_at: string | null;
}

interface Props {
    page: Page;
}

export default function EditPage({ page }: Props) {
    const [previewImage, setPreviewImage] = useState<string | null>(page.featured_image);
    
    const { data, setData, put, post, processing, errors } = useForm({
        title: page.title,
        slug: page.slug,
        content: page.content,
        meta_description: page.meta_description || '',
        meta_keywords: page.meta_keywords || '',
        page_layout: page.page_layout,
        status: page.status,
        publish_immediately: page.publish_immediately,
        is_featured: page.is_featured,
        featured_image: null as File | null,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Always use FormData for consistency (works for both file uploads and regular data)
        const formData = new FormData();

        // Append all form data
        formData.append('title', data.title);
        formData.append('slug', data.slug);
        formData.append('content', data.content);
        formData.append('meta_description', data.meta_description || '');
        formData.append('meta_keywords', data.meta_keywords || '');
        formData.append('page_layout', data.page_layout);
        formData.append('status', data.status);
        formData.append('is_featured', data.is_featured ? '1' : '0');
        formData.append('publish_immediately', data.publish_immediately ? '1' : '0');

        // Append featured image if selected
        if (data.featured_image instanceof File) {
            formData.append('featured_image', data.featured_image);
        }

        // Use router.post with method spoofing (same pattern as projects)
        router.post(route('admin.pages.update', page.id), formData, {
            forceFormData: true,
            onBefore: () => {
                // Add the _method field to make Laravel recognize this as a PUT request
                formData.append('_method', 'PUT');
                return true;
            },
            onSuccess: () => {
                toast.success('Page updated successfully!');

                // Redirect to pages index after successful update
                setTimeout(() => {
                    router.visit(route('admin.pages.index'));
                }, 1500);
            },
            onError: (errors) => {
                console.error('Form errors:', errors);
                toast.error('Failed to update page. Please check the form for errors.');

                // Show specific error messages if available
                if (errors && typeof errors === 'object') {
                    Object.keys(errors).forEach(key => {
                        if (errors[key]) {
                            toast.error(`${key}: ${errors[key]}`);
                        }
                    });
                }
            }
        });
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('featured_image', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreviewImage(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    };

    const handleTitleChange = (title: string) => {
        setData('title', title);
        // Only auto-generate slug if it matches the current generated slug
        const currentGeneratedSlug = generateSlug(page.title);
        if (data.slug === currentGeneratedSlug || data.slug === page.slug) {
            setData('slug', generateSlug(title));
        }
    };

    return (
        <AdminLayout>
            <Head title={`Edit ${page.title}`} />
            
            {/* Dashboard Header */}
            <div className="border-b border-gray-200 bg-white">
                <div className="px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.pages.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">Edit Page</h1>
                                <p className="mt-1 text-sm text-gray-500">Update page content and settings</p>
                            </div>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setData('status', 'draft')}
                                disabled={processing}
                            >
                                <Save className="w-4 h-4 mr-2" />
                                Save as Draft
                            </Button>
                            <Button
                                type="submit"
                                form="page-form"
                                className="bg-[#20B2AA] hover:bg-[#1a9994] text-white"
                                disabled={processing}
                                onClick={() => setData('status', 'published')}
                            >
                                <Eye className="w-4 h-4 mr-2" />
                                Update Page
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <div className="p-6 space-y-6">
                <form id="page-form" onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Page Content Card */}
                        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-[#20B2AA] rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900">Page Content</h2>
                            </div>
                            <p className="text-sm text-gray-600 mb-6">
                                Enter the main content and details for your page
                            </p>

                            <div className="space-y-4">
                                {/* Title */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                        Title *
                                    </label>
                                    <input
                                        type="text"
                                        value={data.title}
                                        onChange={(e) => handleTitleChange(e.target.value)}
                                        placeholder="Enter page title"
                                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                            errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                                        } bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}
                                        required
                                    />
                                    {errors.title && (
                                        <p className="text-red-500 text-sm mt-2">{errors.title}</p>
                                    )}
                                </div>

                                {/* URL Slug */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                        URL Slug *
                                    </label>
                                    <div className="flex">
                                        <span className="inline-flex items-center px-4 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm">
                                            /page/
                                        </span>
                                        <input
                                            type="text"
                                            value={data.slug}
                                            onChange={(e) => setData('slug', e.target.value)}
                                            placeholder="page-url-slug"
                                            className={`flex-1 px-4 py-3 border rounded-r-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                                errors.slug ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                                            } bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}
                                            required
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                        URL-friendly version of the title
                                    </p>
                                    {errors.slug && (
                                        <p className="text-red-500 text-sm mt-2">{errors.slug}</p>
                                    )}
                                </div>

                                {/* Content */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                        Content *
                                    </label>
                                    <RichTextEditor
                                        value={data.content}
                                        onChange={(content) => setData('content', content)}
                                        placeholder="Use the toolbar above to format your content with headings, lists, and more..."
                                        error={errors.content}
                                    />
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                        Use the toolbar above to format your content with headings, lists, and more.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* SEO Settings Card */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
                            <div className="flex items-center gap-2 mb-6">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">SEO Settings</h2>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-8">
                                Optimize your page for search engines
                            </p>

                            <div className="space-y-6">
                                {/* Meta Description */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Meta Description
                                    </label>
                                    <textarea
                                        value={data.meta_description}
                                        onChange={(e) => setData('meta_description', e.target.value)}
                                        placeholder="Brief description for search engines (160 characters max)"
                                        rows={3}
                                        maxLength={160}
                                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all resize-none ${
                                            errors.meta_description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                                        } bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}
                                    />
                                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <span>Brief description for search engines (160 characters max)</span>
                                        <span>{data.meta_description.length}/160</span>
                                    </div>
                                    {errors.meta_description && (
                                        <p className="text-red-500 text-sm mt-1">{errors.meta_description}</p>
                                    )}
                                </div>

                                {/* Meta Keywords */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Meta Keywords
                                    </label>
                                    <input
                                        type="text"
                                        value={data.meta_keywords}
                                        onChange={(e) => setData('meta_keywords', e.target.value)}
                                        placeholder="keyword1, keyword2, keyword3"
                                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all ${
                                            errors.meta_keywords ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                                        } bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}
                                    />
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Separate keywords with commas
                                    </p>
                                    {errors.meta_keywords && (
                                        <p className="text-red-500 text-sm mt-1">{errors.meta_keywords}</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-8">
                        {/* Publishing Card */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
                            <div className="flex items-center gap-2 mb-6">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Publishing</h2>
                            </div>

                            <div className="space-y-6">
                                {/* Current Status */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                        Current Status
                                    </label>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        page.status === 'published'
                                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                                    }`}>
                                        {page.status}
                                    </span>
                                    {page.published_at && (
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Published: {page.published_at}
                                        </p>
                                    )}
                                </div>

                                {/* Page Layout */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                        Page Layout
                                    </label>
                                    <select
                                        value={data.page_layout}
                                        onChange={(e) => setData('page_layout', e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                                    >
                                        <option value="default">Default Layout</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Featured Image Card */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
                            <div className="flex items-center gap-2 mb-6">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Featured Image</h2>
                            </div>

                            <div className="space-y-6">
                                {previewImage && (
                                    <div className="relative">
                                        <img
                                            src={previewImage}
                                            alt="Preview"
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setPreviewImage(null);
                                                setData('featured_image', null);
                                            }}
                                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                                        >
                                            ×
                                        </button>
                                    </div>
                                )}
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Select Image
                                    </label>
                                    <input
                                        type="file"
                                        accept="image/*"
                                        onChange={handleImageChange}
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                                    />
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Or enter image URL
                                    </p>
                                    <input
                                        type="url"
                                        placeholder="Select featured image"
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent outline-none transition-all bg-white dark:bg-gray-900 text-gray-900 dark:text-white mt-2"
                                        disabled
                                    />
                                    {errors.featured_image && (
                                        <p className="text-red-500 text-sm mt-1">{errors.featured_image}</p>
                                    )}
                                </div>

                                {/* Featured Toggle */}
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_featured"
                                        checked={data.is_featured}
                                        onChange={(e) => setData('is_featured', e.target.checked)}
                                        className="w-4 h-4 text-[#20B2AA] bg-gray-100 border-gray-300 rounded focus:ring-[#20B2AA] focus:ring-2"
                                    />
                                    <label htmlFor="is_featured" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Featured page
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
