import { Head, useForm, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
    Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle
} from '@/components/ui/card';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Mail, Send, TestTube, CheckCircle, XCircle, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { motion } from 'framer-motion';

interface SmtpConfig {
    id: number;
    enabled: boolean;
    mailer: string;
    host: string;
    port: number;
    username: string;
    password: string;
    encryption: string;
    from_address: string;
    from_name: string;
    admin_email: string;
    admin_notifications: boolean;
    test_email_subject: string;
    test_email_body: string;
    last_tested_at: string | null;
    last_test_successful: boolean;
    last_test_error: string | null;
}

interface ProviderPreset {
    name: string;
    host: string;
    port: number;
    encryption: string;
}

interface Props {
    smtpConfig: SmtpConfig;
    encryptionOptions: Record<string, string>;
    providerPresets: Record<string, ProviderPreset>;
}

export default function SmtpConfig({ smtpConfig, encryptionOptions, providerPresets }: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [testEmail, setTestEmail] = useState('');
    const [isTesting, setIsTesting] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        enabled: smtpConfig.enabled || false,
        mailer: smtpConfig.mailer || 'smtp',
        host: smtpConfig.host || '',
        port: smtpConfig.port || 587,
        username: smtpConfig.username || '',
        password: '', // Always start empty for security
        encryption: smtpConfig.encryption || 'tls',
        from_address: smtpConfig.from_address || '',
        from_name: smtpConfig.from_name || '',
        admin_email: smtpConfig.admin_email || '',
        admin_notifications: smtpConfig.admin_notifications || true,
        test_email_subject: smtpConfig.test_email_subject || 'Test Email from Portfolio',
        test_email_body: smtpConfig.test_email_body || 'This is a test email to verify SMTP configuration is working correctly.',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        put(route('admin.smtp-config.update'), {
            onSuccess: () => {
                toast.success('SMTP configuration updated successfully');
                router.reload();
            },
            onError: (errors) => {
                console.error('SMTP update errors:', errors);
                toast.error('Failed to update SMTP configuration. Please check the form for errors.');
            },
        });
    };

    const handleProviderChange = (provider: string) => {
        if (provider && providerPresets[provider]) {
            const preset = providerPresets[provider];
            setData({
                ...data,
                host: preset.host,
                port: preset.port,
                encryption: preset.encryption,
            });
        }
    };

    const handleTestEmail = async () => {
        if (!testEmail) {
            toast.error('Please enter an email address for testing');
            return;
        }

        setIsTesting(true);
        
        try {
            const response = await fetch(route('admin.smtp-config.test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ test_email: testEmail }),
            });

            const result = await response.json();

            if (result.success) {
                toast.success(result.message);
                router.reload();
            } else {
                toast.error(result.message);
            }
        } catch (error) {
            toast.error('Failed to send test email');
        } finally {
            setIsTesting(false);
        }
    };

    return (
        <AdminLayout>
            <Head title="SMTP Configuration" />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">SMTP Configuration</h1>
                        <p className="text-gray-600 mt-1">Configure email settings for contact form notifications</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Mail className="w-5 h-5 text-[#20B2AA]" />
                        <span className={`text-sm font-medium ${data.enabled ? 'text-green-600' : 'text-gray-500'}`}>
                            {data.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Status Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Mail className="w-5 h-5" />
                                Email Status & Settings
                            </CardTitle>
                            <CardDescription>
                                Enable or disable email notifications and configure basic settings.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label htmlFor="enabled" className="text-base font-medium">Enable SMTP</Label>
                                    <p className="text-sm text-gray-600">Turn on email notifications for contact form submissions</p>
                                </div>
                                <Switch
                                    id="enabled"
                                    checked={data.enabled}
                                    onCheckedChange={(checked) => setData('enabled', checked)}
                                />
                            </div>

                            {data.enabled && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    className="space-y-4 pt-4 border-t"
                                >
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="admin_email">Admin Email *</Label>
                                            <Input
                                                id="admin_email"
                                                type="email"
                                                value={data.admin_email}
                                                onChange={(e) => setData('admin_email', e.target.value)}
                                                placeholder="<EMAIL>"
                                                required
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Email address to receive contact notifications</p>
                                            {errors.admin_email && (
                                                <p className="text-sm text-red-600 mt-1">{errors.admin_email}</p>
                                            )}
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <Label htmlFor="admin_notifications" className="text-sm font-medium">Admin Notifications</Label>
                                                <p className="text-xs text-gray-600">Send email alerts for new contact messages</p>
                                            </div>
                                            <Switch
                                                id="admin_notifications"
                                                checked={data.admin_notifications}
                                                onCheckedChange={(checked) => setData('admin_notifications', checked)}
                                            />
                                        </div>
                                    </div>
                                </motion.div>
                            )}
                        </CardContent>
                    </Card>

                    {/* SMTP Configuration Card */}
                    {data.enabled && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                        >
                            <Card>
                                <CardHeader>
                                    <CardTitle>SMTP Server Configuration</CardTitle>
                                    <CardDescription>
                                        Configure your SMTP server settings for sending emails.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Provider Presets */}
                                    <div>
                                        <Label htmlFor="provider">Email Provider</Label>
                                        <Select onValueChange={handleProviderChange}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a provider or choose Custom" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(providerPresets).map(([key, preset]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {preset.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <p className="text-xs text-gray-500 mt-1">Choose a preset or select Custom to configure manually</p>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="host">SMTP Host *</Label>
                                            <Input
                                                id="host"
                                                value={data.host}
                                                onChange={(e) => setData('host', e.target.value)}
                                                placeholder="smtp.gmail.com"
                                                required
                                            />
                                            {errors.host && (
                                                <p className="text-sm text-red-600 mt-1">{errors.host}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="port">Port *</Label>
                                            <Input
                                                id="port"
                                                type="number"
                                                value={data.port}
                                                onChange={(e) => setData('port', parseInt(e.target.value))}
                                                placeholder="587"
                                                required
                                            />
                                            {errors.port && (
                                                <p className="text-sm text-red-600 mt-1">{errors.port}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="username">Username *</Label>
                                            <Input
                                                id="username"
                                                value={data.username}
                                                onChange={(e) => setData('username', e.target.value)}
                                                placeholder="<EMAIL>"
                                                required
                                            />
                                            {errors.username && (
                                                <p className="text-sm text-red-600 mt-1">{errors.username}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="password">Password</Label>
                                            <div className="relative">
                                                <Input
                                                    id="password"
                                                    type={showPassword ? 'text' : 'password'}
                                                    value={data.password}
                                                    onChange={(e) => setData('password', e.target.value)}
                                                    placeholder="Leave empty to keep current password"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                            {errors.password && (
                                                <p className="text-sm text-red-600 mt-1">{errors.password}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="encryption">Encryption</Label>
                                        <Select value={data.encryption} onValueChange={(value) => setData('encryption', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(encryptionOptions).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="from_address">From Email *</Label>
                                            <Input
                                                id="from_address"
                                                type="email"
                                                value={data.from_address}
                                                onChange={(e) => setData('from_address', e.target.value)}
                                                placeholder="<EMAIL>"
                                                required
                                            />
                                            {errors.from_address && (
                                                <p className="text-sm text-red-600 mt-1">{errors.from_address}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="from_name">From Name *</Label>
                                            <Input
                                                id="from_name"
                                                value={data.from_name}
                                                onChange={(e) => setData('from_name', e.target.value)}
                                                placeholder="Portfolio Contact"
                                                required
                                            />
                                            {errors.from_name && (
                                                <p className="text-sm text-red-600 mt-1">{errors.from_name}</p>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </motion.div>
                    )}

                    {/* Test Email Card */}
                    {data.enabled && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                        >
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <TestTube className="w-5 h-5" />
                                        Test Email Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Send a test email to verify your SMTP configuration is working correctly.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="test_email_subject">Test Email Subject</Label>
                                            <Input
                                                id="test_email_subject"
                                                value={data.test_email_subject}
                                                onChange={(e) => setData('test_email_subject', e.target.value)}
                                                placeholder="Test Email from Portfolio"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="test_email">Test Email Address</Label>
                                            <Input
                                                id="test_email"
                                                type="email"
                                                value={testEmail}
                                                onChange={(e) => setTestEmail(e.target.value)}
                                                placeholder="<EMAIL>"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="test_email_body">Test Email Body</Label>
                                        <Textarea
                                            id="test_email_body"
                                            value={data.test_email_body}
                                            onChange={(e) => setData('test_email_body', e.target.value)}
                                            placeholder="This is a test email to verify SMTP configuration is working correctly."
                                            rows={3}
                                        />
                                    </div>

                                    {/* Last Test Status */}
                                    {smtpConfig.last_tested_at && (
                                        <div className="bg-gray-50 rounded-lg p-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                {smtpConfig.last_test_successful ? (
                                                    <CheckCircle className="w-5 h-5 text-green-600" />
                                                ) : (
                                                    <XCircle className="w-5 h-5 text-red-600" />
                                                )}
                                                <span className="font-medium">
                                                    Last Test: {smtpConfig.last_test_successful ? 'Successful' : 'Failed'}
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    {smtpConfig.last_tested_at}
                                                </span>
                                            </div>
                                            {!smtpConfig.last_test_successful && smtpConfig.last_test_error && (
                                                <div className="text-sm text-red-600 bg-red-50 p-2 rounded border">
                                                    <strong>Error:</strong> {smtpConfig.last_test_error}
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    <div className="flex justify-end">
                                        <Button
                                            type="button"
                                            onClick={handleTestEmail}
                                            disabled={isTesting || !testEmail}
                                            variant="outline"
                                            className="flex items-center gap-2"
                                        >
                                            <Send className="w-4 h-4" />
                                            {isTesting ? 'Sending...' : 'Send Test Email'}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </motion.div>
                    )}

                    {/* Save Button */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing} className="bg-[#20B2AA] hover:bg-[#1a9994]">
                            {processing ? 'Saving...' : 'Save Configuration'}
                        </Button>
                    </div>
                </form>

                {/* Help Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <AlertCircle className="w-5 h-5" />
                            Setup Help
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4 text-sm">
                            <div>
                                <h4 className="font-medium mb-2">Gmail Setup:</h4>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Use your Gmail address as username</li>
                                    <li>Generate an App Password in your Google Account settings</li>
                                    <li>Use the App Password instead of your regular password</li>
                                    <li>Enable 2-factor authentication if not already enabled</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Common Issues:</h4>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Make sure your hosting provider allows outbound SMTP connections</li>
                                    <li>Check if port 587 or 465 is blocked by your firewall</li>
                                    <li>Verify your email credentials are correct</li>
                                    <li>Some providers require specific authentication methods</li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
