import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import TestimonialsManagement from '../testimonials/management';
import TestimonialsContentManagement from '../testimonials/content';

interface Testimonial {
    id: number;
    name: string;
    position: string;
    company: string;
    quote: string;
    rating: number;
    image: string | null;
    order: number;
}

interface Props {
    testimonials: Testimonial[];
}

export default function Testimonials({ testimonials }: Props) {
    return (
        <AdminLayout>
            <Head title="Testimonials - Portfolio Admin" />
            
            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-semibold tracking-tight text-gray-900">Testimonials</h1>
                        <p className="text-sm text-gray-500">Manage client testimonials and section content</p>
                    </div>
                </div>

                {/* Content */}
                <Card className="p-6">
                    <Tabs defaultValue="management" className="w-full">
                        <TabsList className="mb-4">
                            <TabsTrigger value="management">Testimonials</TabsTrigger>
                            <TabsTrigger value="content">Content</TabsTrigger>
                        </TabsList>
                        <TabsContent value="management">
                            <TestimonialsManagement testimonials={testimonials} />
                        </TabsContent>
                        <TabsContent value="content">
                            <TestimonialsContentManagement />
                        </TabsContent>
                    </Tabs>
                </Card>
            </div>
        </AdminLayout>
    );
}
