import { Head } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Calendar, User, Tag, ArrowUp, Menu, X } from 'lucide-react';
import { ThemeProvider } from '@/components/theme-provider';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import { ThemeToggle } from '@/components/theme-toggle';
import { NavigationMenu, NavigationMenuList, NavigationMenuItem } from '@/components/ui/navigation-menu';
import { animateScroll as scroll } from 'react-scroll';
import { Button } from '@/components/ui/button';
import { Link as ScrollLink } from 'react-scroll';

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string;
    meta_description: string | null;
    meta_keywords: string | null;
    featured_image: string | null;
    page_layout: string;
    published_at: string | null;
    is_featured: boolean;
}

interface ProfileData {
    page_title: string;
    title: string;
    about: string;
    years_experience: number;
    projects_completed: number;
    is_available: boolean;
    cta_text: string;
    cta_secondary_text: string;
    cta_url: string;
    cta_secondary_url: string;
    avatar: string;
    logo: {
        text: string;
        type: string;
        icon: string;
        icon_type: string;
        color: string;
    };
    navbar_items: Array<{
        title: string;
        href: string;
    }>;
    hire_me: {
        text: string;
        url: string;
        enabled: boolean;
    };
    social_links: any[];
}

interface Props {
    page: Page;
    profile: ProfileData;
    services: any[];
    projects: any[];
    testimonials: any[];
    skills: any[];
    experiences: any[];
    education: any[];
    resumeContent: any;
    testimonialsContent: any;
}

export default function PageWithHomeLayout({ page, profile, services, projects, testimonials, skills, experiences, education, resumeContent, testimonialsContent }: Props) {
    const [showScrollTop, setShowScrollTop] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const { scrollY } = useScroll();

    // Navigation animation values
    const navbarBackground = useTransform(
        scrollY,
        [0, 100],
        ["rgba(255, 255, 255, 0.8)", "rgba(255, 255, 255, 0.95)"]
    );
    const navbarHeight = useTransform(scrollY, [0, 100], ["80px", "64px"]);
    const navbarShadow = useTransform(
        scrollY,
        [0, 100],
        ["0 0 0 rgba(0, 0, 0, 0)", "0 4px 20px rgba(0, 0, 0, 0.1)"]
    );

    // Logo configuration
    const logoText = profile?.logo?.text || 'Portfolio';
    const logoType = profile?.logo?.type || 'text_only';
    const logoIcon = profile?.logo?.icon || 'P';
    const logoColor = profile?.logo?.color || '#20B2AA';
    const logoIconType = profile?.logo?.icon_type || 'letter';

    // Navigation items - convert to home page links
    const defaultNavItems = [
        { title: 'Home', href: '/' },
        { title: 'Services', href: '/#services' },
        { title: 'Projects', href: '/#works' },
        { title: 'Skills', href: '/#skills' },
        { title: 'Resume', href: '/#resume' },
        { title: 'Testimonials', href: '/#testimonials' },
        { title: 'Contact', href: '/#contact' }
    ];

    let navItems = defaultNavItems;
    if (profile?.navbar_items && Array.isArray(profile.navbar_items)) {
        navItems = profile.navbar_items.map(item => ({
            title: item.title,
            href: item.href === 'home' ? '/' : `/#${item.href}`
        }));
    }

    // Hire me button settings
    const hireMeText = profile?.hire_me?.text || 'Hire Me';
    const hireMeUrl = profile?.hire_me?.url || '/#contact';
    const hireMeEnabled = profile?.hire_me?.enabled ?? true;

    // Show/hide scroll to top button
    useEffect(() => {
        const handleScrollVisibility = () => {
            if (window.scrollY > 500) {
                setShowScrollTop(true);
            } else {
                setShowScrollTop(false);
            }
        };

        window.addEventListener('scroll', handleScrollVisibility);
        return () => window.removeEventListener('scroll', handleScrollVisibility);
    }, []);

    const scrollToTop = () => {
        scroll.scrollToTop({
            duration: 500,
            smooth: true
        });
    };

    return (
        <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
            <Head
                title={`${page.title} - ${profile.page_title}`}
                description={page.meta_description || `Read ${page.title} on ${profile.page_title}`}
            >
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
                {page.meta_keywords && (
                    <meta name="keywords" content={page.meta_keywords} />
                )}
                <meta property="og:title" content={page.title} />
                <meta property="og:description" content={page.meta_description || `Read ${page.title} on ${profile.page_title}`} />
                {page.featured_image && (
                    <meta property="og:image" content={page.featured_image} />
                )}
                <meta property="og:type" content="article" />
            </Head>

            <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                {/* Navigation */}
                <motion.nav
                    style={{
                        backgroundColor: navbarBackground,
                        height: navbarHeight,
                        boxShadow: navbarShadow,
                    }}
                    className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 sm:px-6 md:px-8 py-4 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 transition-all duration-300 dark:bg-transparent"
                >
                    <motion.div
                        className="absolute inset-0 z-[-1] dark:bg-gray-900/95 backdrop-blur-sm"
                    />

                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.location.href = '/'}
                        className="cursor-pointer"
                    >
                        <DynamicLogo
                            logoText={logoText}
                            logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                            logoIcon={logoIcon}
                            logoIconType={logoIconType as 'letter' | 'svg'}
                            logoColor={logoColor}
                        />
                    </motion.div>

                    {/* Desktop Navigation */}
                    <div className="hidden md:block">
                        <NavigationMenu>
                            <NavigationMenuList className="flex gap-4 lg:gap-8">
                                {navItems.map((item, index) => (
                                    <NavigationMenuItem key={item.title}>
                                        <motion.div
                                            initial={{ opacity: 0, y: -20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: index * 0.1 }}
                                        >
                                            <motion.div whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 400 }}>
                                                <a
                                                    href={item.href}
                                                    className="text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] cursor-pointer font-medium text-sm transition-colors px-2 py-1.5 block"
                                                >
                                                    {item.title}
                                                </a>
                                            </motion.div>
                                        </motion.div>
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    <motion.div className="flex items-center space-x-2">
                        {/* Mobile menu button */}
                        <motion.button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="md:hidden p-2 text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] transition-colors"
                            whileTap={{ scale: 0.95 }}
                        >
                            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                        </motion.button>

                        <ThemeToggle />
                        {hireMeEnabled && (
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Button className="hidden md:inline-flex bg-[#20B2AA] hover:bg-[#1a9994] text-white shadow-md shadow-[#20B2AA]/20 dark:shadow-[#20B2AA]/40 hover:shadow-lg hover:shadow-[#20B2AA]/30 dark:hover:shadow-[#20B2AA]/50 transition-all duration-300">
                                    {hireMeUrl.startsWith('#') ? (
                                        <ScrollLink
                                            to={hireMeUrl.substring(1)}
                                            spy={true}
                                            smooth={true}
                                            offset={-100}
                                            duration={500}
                                            className="cursor-pointer"
                                        >
                                            {hireMeText}
                                        </ScrollLink>
                                    ) : (
                                        <a href={hireMeUrl} className="cursor-pointer">
                                            {hireMeText}
                                        </a>
                                    )}
                                </Button>
                            </motion.div>
                        )}
                    </motion.div>
                </motion.nav>

                {/* Custom Mobile Menu for Page Layout */}
                {isMenuOpen && (
                    <div className="fixed inset-0 z-40 md:hidden">
                        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsMenuOpen(false)} />
                        <div className="fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-900 shadow-xl">
                            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                                <DynamicLogo
                                    logoText={logoText}
                                    logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                                    logoIcon={logoIcon}
                                    logoIconType={logoIconType as 'letter' | 'svg'}
                                    logoColor={logoColor}
                                />
                                <button
                                    onClick={() => setIsMenuOpen(false)}
                                    className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                                >
                                    <X className="w-6 h-6" />
                                </button>
                            </div>
                            <nav className="p-6">
                                {navItems.map((item) => (
                                    <a
                                        key={item.title}
                                        href={item.href}
                                        className="block py-3 px-4 text-gray-700 dark:text-gray-300 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                                        onClick={() => setIsMenuOpen(false)}
                                    >
                                        {item.title}
                                    </a>
                                ))}
                                {hireMeEnabled && (
                                    <a
                                        href={hireMeUrl}
                                        className="block mt-6 py-3 px-4 bg-[#20B2AA] text-white text-center rounded-lg hover:bg-[#1a9994] transition-colors"
                                        onClick={() => setIsMenuOpen(false)}
                                    >
                                        {hireMeText}
                                    </a>
                                )}
                            </nav>
                        </div>
                    </div>
                )}

                <main className="pt-20">
                    {/* Back to Home Button - Top Left */}
                    <div className="fixed top-24 left-6 z-30">
                        <motion.a
                            href="/"
                            className="inline-flex items-center px-4 py-2 bg-[#20B2AA] text-white rounded-full font-medium hover:bg-[#1a9994] transition-all duration-200 shadow-lg hover:shadow-xl"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2 }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            ← Back to Home
                        </motion.a>
                    </div>

                    {/* Page Hero Section */}
                    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                {page.is_featured && (
                                    <motion.div
                                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#20B2AA] text-white mb-4"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <Tag className="w-4 h-4 mr-1" />
                                        Featured Page
                                    </motion.div>
                                )}

                                <motion.h1
                                    className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    {page.title}
                                </motion.h1>

                                {page.meta_description && (
                                    <motion.p
                                        className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        {page.meta_description}
                                    </motion.p>
                                )}

                                <motion.div
                                    className="flex items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    {page.published_at && (
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4" />
                                            <span>Published {page.published_at}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        <span>{profile.page_title}</span>
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Featured Image */}
                    {page.featured_image && (
                        <section className="py-8">
                            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                                <motion.div
                                    className="relative rounded-2xl overflow-hidden shadow-2xl"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.6 }}
                                >
                                    <img
                                        src={page.featured_image}
                                        alt={page.title}
                                        className="w-full h-64 md:h-96 object-cover"
                                    />
                                </motion.div>
                            </div>
                        </section>
                    )}

                    {/* Page Content */}
                    <section className="py-16">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="prose prose-lg max-w-none dark:prose-invert
                                prose-headings:text-gray-900 dark:prose-headings:text-white prose-headings:font-bold
                                prose-h1:text-4xl prose-h1:mb-6 prose-h1:mt-8
                                prose-h2:text-3xl prose-h2:mb-4 prose-h2:mt-6
                                prose-h3:text-2xl prose-h3:mb-3 prose-h3:mt-5
                                prose-h4:text-xl prose-h4:mb-2 prose-h4:mt-4
                                prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-4
                                prose-a:text-[#20B2AA] prose-a:no-underline hover:prose-a:underline prose-a:font-medium
                                prose-strong:text-gray-900 dark:prose-strong:text-white prose-strong:font-semibold
                                prose-em:text-gray-700 dark:prose-em:text-gray-300 prose-em:italic
                                prose-code:text-[#20B2AA] prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm
                                prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto
                                prose-blockquote:border-l-4 prose-blockquote:border-[#20B2AA] prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300
                                prose-ul:list-disc prose-ul:pl-6 prose-ul:mb-4
                                prose-ol:list-decimal prose-ol:pl-6 prose-ol:mb-4
                                prose-li:mb-2 prose-li:text-gray-700 dark:prose-li:text-gray-300
                                prose-table:w-full prose-table:border-collapse
                                prose-th:border prose-th:border-gray-300 dark:prose-th:border-gray-600 prose-th:p-2 prose-th:bg-gray-50 dark:prose-th:bg-gray-800
                                prose-td:border prose-td:border-gray-300 dark:prose-td:border-gray-600 prose-td:p-2
                                prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto"
                                dangerouslySetInnerHTML={{ __html: page.content }}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 }}
                            />
                        </div>
                    </section>


                </main>

                {/* Scroll to Top Button */}
                {showScrollTop && (
                    <motion.button
                        onClick={scrollToTop}
                        className="fixed bottom-8 right-8 z-40 p-3 bg-[#20B2AA] text-white rounded-full shadow-lg hover:bg-[#1a9994] transition-colors"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <ArrowUp className="w-5 h-5" />
                    </motion.button>
                )}

                {/* Footer */}
                <footer className="bg-[#0F172A] dark:bg-gray-950 text-white py-8 sm:py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-12 mb-6 sm:mb-8">
                            {/* Left Column - Logo and Description */}
                            <div>
                                <div className="flex items-center gap-2 mb-3 sm:mb-4">
                                    <div className="w-6 h-6 sm:w-7 sm:h-7 bg-[#20B2AA] rounded-full flex items-center justify-center text-white font-bold text-xs sm:text-sm">
                                        {logoText.charAt(0) || 'P'}
                                    </div>
                                    <span className="font-medium text-xs sm:text-sm">{logoText || 'Portfolio'}</span>
                                </div>
                                <p className="text-gray-400 max-w-md text-xs sm:text-sm">
                                    Creating exceptional digital experiences through innovative design and development solutions.
                                </p>
                            </div>

                            {/* Right Column - Quick Links */}
                            <div>
                                <h3 className="text-sm sm:text-base font-semibold mb-3 sm:mb-4">Quick Links</h3>
                                <div className="grid grid-cols-2 gap-2 sm:gap-4">
                                    {navItems.slice(0, 4).map((item) => (
                                        <a
                                            key={item.title}
                                            href={item.href}
                                            className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors"
                                        >
                                            {item.title}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Bottom Row */}
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
                            <div className="text-gray-400 text-xs sm:text-sm order-2 sm:order-1">
                                © {new Date().getFullYear()} {profile.page_title}. All rights reserved.
                            </div>
                            <div className="flex items-center flex-wrap justify-center gap-4 sm:gap-8 order-1 sm:order-2">
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Privacy Policy
                                </a>
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Terms of Service
                                </a>
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Cookie Policy
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </ThemeProvider>
    );
}
