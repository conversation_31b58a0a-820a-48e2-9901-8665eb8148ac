import { Head } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Calendar, User, Tag, ArrowUp, Menu, X, Github, Linkedin, Twitter, Facebook, Instagram, ArrowLeft } from 'lucide-react';
import { ThemeProvider } from '@/components/theme-provider';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import { ThemeToggle } from '@/components/theme-toggle';
import { NavigationMenu, NavigationMenuList, NavigationMenuItem } from '@/components/ui/navigation-menu';
import { animateScroll as scroll } from 'react-scroll';
import { Button } from '@/components/ui/button';
import { Link as ScrollLink } from 'react-scroll';

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string;
    meta_description: string | null;
    meta_keywords: string | null;
    featured_image: string | null;
    page_layout: string;
    published_at: string | null;
    is_featured: boolean;
}

interface ProfileData {
    page_title: string;
    title: string;
    about: string;
    years_experience: number;
    projects_completed: number;
    is_available: boolean;
    cta_text: string;
    cta_secondary_text: string;
    cta_url: string;
    cta_secondary_url: string;
    avatar: string;
    logo: {
        text: string;
        type: string;
        icon: string;
        icon_type: string;
        color: string;
    };
    navbar_items: Array<{
        title: string;
        href: string;
    }>;
    hire_me: {
        text: string;
        url: string;
        enabled: boolean;
    };
    social: {
        github: string | null;
        twitter: string | null;
        linkedin: string | null;
        facebook: string | null;
        instagram: string | null;
        dribbble: string | null;
        behance: string | null;
        upassign: string | null;
        fiverr: string | null;
        upwork: string | null;
        freelancer: string | null;
        peopleperhour: string | null;
    };
}

interface Props {
    page: Page;
    profile: ProfileData;
    services: any[];
    projects: any[];
    testimonials: any[];
    skills: any[];
    experiences: any[];
    education: any[];
    resumeContent: any;
    testimonialsContent: any;
}

export default function PageWithHomeLayout({ page, profile, services, projects, testimonials, skills, experiences, education, resumeContent, testimonialsContent }: Props) {
    const [showScrollTop, setShowScrollTop] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const { scrollY } = useScroll();

    // Navigation animation values
    const navbarBackground = useTransform(
        scrollY,
        [0, 100],
        ["rgba(255, 255, 255, 0.8)", "rgba(255, 255, 255, 0.95)"]
    );
    const navbarHeight = useTransform(scrollY, [0, 100], ["80px", "64px"]);
    const navbarShadow = useTransform(
        scrollY,
        [0, 100],
        ["0 0 0 rgba(0, 0, 0, 0)", "0 4px 20px rgba(0, 0, 0, 0.1)"]
    );

    // Logo configuration
    const logoText = profile?.logo?.text || 'Portfolio';
    const logoType = profile?.logo?.type || 'text_only';
    const logoIcon = profile?.logo?.icon || 'P';
    const logoColor = profile?.logo?.color || '#20B2AA';
    const logoIconType = profile?.logo?.icon_type || 'letter';

    // Navigation items - convert to home page links
    const defaultNavItems = [
        { title: 'Home', href: '/' },
        { title: 'Services', href: '/#services' },
        { title: 'Projects', href: '/#works' },
        { title: 'Skills', href: '/#skills' },
        { title: 'Resume', href: '/#resume' },
        { title: 'Testimonials', href: '/#testimonials' },
        { title: 'Contact', href: '/#contact' }
    ];

    let navItems = defaultNavItems;
    if (profile?.navbar_items && Array.isArray(profile.navbar_items)) {
        navItems = profile.navbar_items.map(item => ({
            title: item.title,
            href: item.href === 'home' ? '/' : `/#${item.href}`
        }));
    }

    // Hire me button settings
    const hireMeText = profile?.hire_me?.text || 'Hire Me';
    const hireMeUrl = profile?.hire_me?.url || '/#contact';
    const hireMeEnabled = profile?.hire_me?.enabled ?? true;

    // Social media URLs
    const githubUrl = profile?.social?.github;
    const twitterUrl = profile?.social?.twitter;
    const linkedinUrl = profile?.social?.linkedin;
    const facebookUrl = profile?.social?.facebook;
    const instagramUrl = profile?.social?.instagram;
    const dribbbleUrl = profile?.social?.dribbble;
    const behanceUrl = profile?.social?.behance;
    const upassignUrl = profile?.social?.upassign;
    const fiverrUrl = profile?.social?.fiverr;
    const upworkUrl = profile?.social?.upwork;
    const freelancerUrl = profile?.social?.freelancer;
    const peopleperhourUrl = profile?.social?.peopleperhour;

    // Show/hide scroll to top button
    useEffect(() => {
        const handleScrollVisibility = () => {
            if (window.scrollY > 500) {
                setShowScrollTop(true);
            } else {
                setShowScrollTop(false);
            }
        };

        window.addEventListener('scroll', handleScrollVisibility);
        return () => window.removeEventListener('scroll', handleScrollVisibility);
    }, []);

    const scrollToTop = () => {
        scroll.scrollToTop({
            duration: 500,
            smooth: true
        });
    };

    return (
        <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
            <Head
                title={`${page.title} - ${profile.page_title}`}
                description={page.meta_description || `Read ${page.title} on ${profile.page_title}`}
            >
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
                {page.meta_keywords && (
                    <meta name="keywords" content={page.meta_keywords} />
                )}
                <meta property="og:title" content={page.title} />
                <meta property="og:description" content={page.meta_description || `Read ${page.title} on ${profile.page_title}`} />
                {page.featured_image && (
                    <meta property="og:image" content={page.featured_image} />
                )}
                <meta property="og:type" content="article" />
            </Head>

            <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                {/* Navigation */}
                <motion.nav
                    style={{
                        backgroundColor: navbarBackground,
                        height: navbarHeight,
                        boxShadow: navbarShadow,
                    }}
                    className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 sm:px-6 md:px-8 py-4 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 transition-all duration-300 dark:bg-transparent"
                >
                    <motion.div
                        className="absolute inset-0 z-[-1] dark:bg-gray-900/95 backdrop-blur-sm"
                    />

                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.location.href = '/'}
                        className="cursor-pointer"
                    >
                        <DynamicLogo
                            logoText={logoText}
                            logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                            logoIcon={logoIcon}
                            logoIconType={logoIconType as 'letter' | 'svg'}
                            logoColor={logoColor}
                        />
                    </motion.div>

                    {/* Desktop Navigation */}
                    <div className="hidden md:block">
                        <NavigationMenu>
                            <NavigationMenuList className="flex gap-4 lg:gap-8">
                                {navItems.map((item, index) => (
                                    <NavigationMenuItem key={item.title}>
                                        <motion.div
                                            initial={{ opacity: 0, y: -20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: index * 0.1 }}
                                        >
                                            <motion.div whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 400 }}>
                                                <a
                                                    href={item.href}
                                                    className="text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] cursor-pointer font-medium text-sm transition-colors px-2 py-1.5 block"
                                                >
                                                    {item.title}
                                                </a>
                                            </motion.div>
                                        </motion.div>
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    <motion.div className="flex items-center space-x-2">
                        {/* Mobile menu button */}
                        <motion.button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="md:hidden p-2 text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] transition-colors"
                            whileTap={{ scale: 0.95 }}
                        >
                            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                        </motion.button>

                        <ThemeToggle />
                        {hireMeEnabled && (
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Button className="hidden md:inline-flex bg-[#20B2AA] hover:bg-[#1a9994] text-white shadow-md shadow-[#20B2AA]/20 dark:shadow-[#20B2AA]/40 hover:shadow-lg hover:shadow-[#20B2AA]/30 dark:hover:shadow-[#20B2AA]/50 transition-all duration-300">
                                    {hireMeUrl.startsWith('#') ? (
                                        <ScrollLink
                                            to={hireMeUrl.substring(1)}
                                            spy={true}
                                            smooth={true}
                                            offset={-100}
                                            duration={500}
                                            className="cursor-pointer"
                                        >
                                            {hireMeText}
                                        </ScrollLink>
                                    ) : (
                                        <a href={hireMeUrl} className="cursor-pointer">
                                            {hireMeText}
                                        </a>
                                    )}
                                </Button>
                            </motion.div>
                        )}
                    </motion.div>
                </motion.nav>

                {/* Custom Mobile Menu for Page Layout */}
                {isMenuOpen && (
                    <div className="fixed inset-0 z-40 md:hidden">
                        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsMenuOpen(false)} />
                        <div className="fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-900 shadow-xl">
                            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                                <DynamicLogo
                                    logoText={logoText}
                                    logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                                    logoIcon={logoIcon}
                                    logoIconType={logoIconType as 'letter' | 'svg'}
                                    logoColor={logoColor}
                                />
                                <button
                                    onClick={() => setIsMenuOpen(false)}
                                    className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                                >
                                    <X className="w-6 h-6" />
                                </button>
                            </div>
                            <nav className="p-6">
                                {navItems.map((item) => (
                                    <a
                                        key={item.title}
                                        href={item.href}
                                        className="block py-3 px-4 text-gray-700 dark:text-gray-300 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                                        onClick={() => setIsMenuOpen(false)}
                                    >
                                        {item.title}
                                    </a>
                                ))}
                                {hireMeEnabled && (
                                    <a
                                        href={hireMeUrl}
                                        className="block mt-6 py-3 px-4 bg-[#20B2AA] text-white text-center rounded-lg hover:bg-[#1a9994] transition-colors"
                                        onClick={() => setIsMenuOpen(false)}
                                    >
                                        {hireMeText}
                                    </a>
                                )}
                            </nav>
                        </div>
                    </div>
                )}

                <main className="pt-20">
                    {/* Back to Home Button - Left Side */}
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-4">
                        <motion.a
                            href="/"
                            className="inline-flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-[#20B2AA] transition-colors duration-200 font-medium"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2 }}
                            whileHover={{ x: -2 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            <ArrowLeft className="w-4 h-4" />
                            Back to Home
                        </motion.a>
                    </div>

                    {/* Page Hero Section */}
                    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                {page.is_featured && (
                                    <motion.div
                                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#20B2AA] text-white mb-4"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <Tag className="w-4 h-4 mr-1" />
                                        Featured Page
                                    </motion.div>
                                )}

                                <motion.h1
                                    className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    {page.title}
                                </motion.h1>

                                {page.meta_description && (
                                    <motion.p
                                        className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        {page.meta_description}
                                    </motion.p>
                                )}

                                <motion.div
                                    className="flex items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    {page.published_at && (
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4" />
                                            <span>Published {page.published_at}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        <span>{profile.page_title}</span>
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Featured Image */}
                    {page.featured_image && (
                        <section className="py-8">
                            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                                <motion.div
                                    className="relative rounded-2xl overflow-hidden shadow-2xl"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.6 }}
                                >
                                    <img
                                        src={page.featured_image}
                                        alt={page.title}
                                        className="w-full h-64 md:h-96 object-cover"
                                    />
                                </motion.div>
                            </div>
                        </section>
                    )}

                    {/* Page Content */}
                    <section className="py-16">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="prose prose-lg max-w-none dark:prose-invert
                                prose-headings:text-gray-900 dark:prose-headings:text-white prose-headings:font-bold
                                prose-h1:text-4xl prose-h1:mb-6 prose-h1:mt-8
                                prose-h2:text-3xl prose-h2:mb-4 prose-h2:mt-6
                                prose-h3:text-2xl prose-h3:mb-3 prose-h3:mt-5
                                prose-h4:text-xl prose-h4:mb-2 prose-h4:mt-4
                                prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-4
                                prose-a:text-[#20B2AA] prose-a:no-underline hover:prose-a:underline prose-a:font-medium
                                prose-strong:text-gray-900 dark:prose-strong:text-white prose-strong:font-semibold
                                prose-em:text-gray-700 dark:prose-em:text-gray-300 prose-em:italic
                                prose-code:text-[#20B2AA] prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm
                                prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto
                                prose-blockquote:border-l-4 prose-blockquote:border-[#20B2AA] prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300
                                prose-ul:list-disc prose-ul:pl-6 prose-ul:mb-4
                                prose-ol:list-decimal prose-ol:pl-6 prose-ol:mb-4
                                prose-li:mb-2 prose-li:text-gray-700 dark:prose-li:text-gray-300
                                prose-table:w-full prose-table:border-collapse
                                prose-th:border prose-th:border-gray-300 dark:prose-th:border-gray-600 prose-th:p-2 prose-th:bg-gray-50 dark:prose-th:bg-gray-800
                                prose-td:border prose-td:border-gray-300 dark:prose-td:border-gray-600 prose-td:p-2
                                prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto"
                                dangerouslySetInnerHTML={{ __html: page.content }}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 }}
                            />
                        </div>
                    </section>


                </main>

                {/* Scroll to Top Button */}
                {showScrollTop && (
                    <motion.button
                        onClick={scrollToTop}
                        className="fixed bottom-8 right-8 z-40 p-3 bg-[#20B2AA] text-white rounded-full shadow-lg hover:bg-[#1a9994] transition-colors"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <ArrowUp className="w-5 h-5" />
                    </motion.button>
                )}

                {/* Footer Section */}
                <footer className="bg-[#0F172A] dark:bg-gray-950 text-white py-8 sm:py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-12 mb-6 sm:mb-8">
                            {/* Left Column - Logo and Description */}
                            <div>
                                <div className="flex items-center gap-2 mb-3 sm:mb-4">
                                    <div className="w-6 h-6 sm:w-7 sm:h-7 bg-[#20B2AA] rounded-full flex items-center justify-center text-white font-bold text-xs sm:text-sm">
                                        P
                                    </div>
                                    <span className="font-medium text-xs sm:text-sm">Portfolio</span>
                                </div>
                                <p className="text-gray-400 max-w-md text-xs sm:text-sm">
                                    Creating exceptional digital experiences through innovative design and development solutions.
                                </p>
                            </div>

                            {/* Right Column - Social Links */}
                            <div className="flex justify-start sm:justify-end mt-4 sm:mt-0">
                                <div className="flex items-center gap-3 sm:gap-4">
                                    {twitterUrl && (
                                        <a href={twitterUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <Twitter className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                                        </a>
                                    )}
                                    {githubUrl && (
                                        <a href={githubUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <Github className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                                        </a>
                                    )}
                                    {linkedinUrl && (
                                        <a href={linkedinUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <Linkedin className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                                        </a>
                                    )}
                                    {facebookUrl && (
                                        <a href={facebookUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <Facebook className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                                        </a>
                                    )}
                                    {instagramUrl && (
                                        <a href={instagramUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <Instagram className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                                        </a>
                                    )}
                                    {dribbbleUrl && (
                                        <a href={dribbbleUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {behanceUrl && (
                                        <a href={behanceUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M6.938 4.503c.702 0 1.34.06 1.92.188.577.13 1.07.33 1.485.61.41.28.733.65.96 1.12.225.47.34 1.05.34 1.73 0 .74-.17 1.36-.507 1.86-.338.5-.837.9-1.502 1.22.906.26 1.576.72 2.022 1.37.448.66.665 1.45.665 2.36 0 .75-.13 1.39-.41 1.93-.28.55-.67 1-.17 1.35-.5.35-1.1.6-1.8.76-.69.16-1.45.24-2.28.24H0V4.51h6.938v-.007zM3.495 8.21h2.881c.795 0 1.393-.155 1.795-.465.402-.31.603-.8.603-1.46 0-.43-.083-.78-.25-1.05-.166-.27-.402-.48-.708-.63-.305-.15-.67-.26-1.093-.33-.424-.07-.896-.105-1.416-.105H3.495v4.04zm0 7.32h3.42c.483 0 .928-.05 1.34-.15.41-.1.763-.25 1.058-.45.295-.2.525-.46.69-.78.165-.32.248-.71.248-1.15 0-.48-.073-.89-.22-1.23-.147-.34-.353-.62-.618-.84-.266-.22-.58-.38-.944-.48-.363-.1-.77-.15-1.217-.15H3.495v5.23zm14.776-8.68c.928 0 1.763.156 2.507.47.744.313 1.375.75 1.893 1.312.518.563.918 1.23 1.2 2.003.282.773.423 1.62.423 2.542 0 .135-.007.27-.02.405-.014.135-.035.27-.063.405H16.99c.07.943.35 1.663.84 2.16.49.497 1.17.745 2.04.745.563 0 1.058-.11 1.485-.33.427-.22.77-.5 1.028-.84h2.566c-.49.943-1.185 1.688-2.085 2.235-.9.547-2.015.82-3.345.82-.773 0-1.478-.12-2.115-.36-.637-.24-1.186-.59-1.647-1.05-.46-.46-.816-1.02-1.068-1.68-.252-.66-.378-1.41-.378-2.25 0-.85.126-1.615.378-2.295.252-.68.608-1.26 1.068-1.725.46-.465 1.01-.825 1.647-1.065.637-.24 1.342-.36 2.115-.36zm2.37 4.995c-.07-.838-.35-1.493-.84-1.965-.49-.472-1.12-.708-1.89-.708-.42 0-.798.08-1.133.24-.335.16-.622.38-.861.66-.24.28-.426.61-.558.99-.132.38-.218.79-.258 1.23h5.54v.553zm-8.24-5.39v1.575h4.305v-1.575H12.4z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {upassignUrl && (
                                        <a href={upassignUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13h2v2h-2V7zm0 4h2v6h-2v-6z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {fiverrUrl && (
                                        <a href={fiverrUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12.036 0C5.388 0 0 5.388 0 12.036s5.388 12.036 12.036 12.036 12.036-5.388 12.036-12.036S18.684 0 12.036 0zm5.4 17.1h-2.16v-1.44c-.72.96-1.68 1.68-3.24 1.68-2.4 0-3.96-1.8-3.96-4.32V8.1h2.16v4.56c0 1.44.72 2.4 2.16 2.4 1.44 0 2.88-1.2 2.88-3.12V8.1h2.16v9zm-10.8-9h2.16v9H6.636v-9z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {upworkUrl && (
                                        <a href={upworkUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.492 0 2.703 1.212 2.703 2.703-.001 1.489-1.212 2.702-2.704 2.702zm0-8.14c-2.539 0-4.51 1.649-5.31 4.366-1.22-1.834-2.148-4.036-2.687-5.892H7.828v7.112c-.002 1.406-1.141 2.546-2.547 2.548-1.405-.002-2.543-1.143-2.545-2.548V3.492H0v7.112c0 2.914 2.37 5.303 5.281 5.303 2.913 0 5.283-2.389 5.283-5.303v-1.19c.529 1.107 1.182 2.229 1.974 3.221l-1.673 7.873h2.797l1.213-5.71c1.063.679 2.285 1.109 3.686 1.109 3 0 5.439-2.452 5.439-5.45 0-3.002-2.439-5.453-5.439-5.453z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {freelancerUrl && (
                                        <a href={freelancerUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M14.096 3.076c1.634.545 2.929 1.841 3.474 3.474.545 1.634.545 3.365 0 5-.545 1.634-1.841 2.929-3.474 3.474-1.634.545-3.365.545-5 0-1.634-.545-2.929-1.841-3.474-3.474-.545-1.634-.545-3.365 0-5 .545-1.634 1.841-2.929 3.474-3.474 1.634-.545 3.365-.545 5 0zm-2.548 7.924c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1z"/>
                                            </svg>
                                        </a>
                                    )}
                                    {peopleperhourUrl && (
                                        <a href={peopleperhourUrl} target="_blank" rel="noopener noreferrer" className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors">
                                            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10zm-1-16h2v2h-2V6zm0 4h2v8h-2v-8z"/>
                                            </svg>
                                        </a>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Divider */}
                        <div className="h-px bg-gray-800 mb-6 sm:mb-8"></div>

                        {/* Bottom Row */}
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
                            <div className="text-gray-400 text-xs sm:text-sm order-2 sm:order-1">
                                © {new Date().getFullYear()} Your Portfolio. All rights reserved.
                            </div>
                            <div className="flex items-center flex-wrap justify-center gap-4 sm:gap-8 order-1 sm:order-2">
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Privacy Policy
                                </a>
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Terms of Service
                                </a>
                                <a href="#" className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors">
                                    Cookie Policy
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </ThemeProvider>
    );
}
