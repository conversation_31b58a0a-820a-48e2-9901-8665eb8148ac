import { Head } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Calendar, User, Tag, ArrowUp } from 'lucide-react';
import { ThemeProvider } from '@/components/theme-provider';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import { ThemeToggle } from '@/components/theme-toggle';
import { NavigationMenu, NavigationMenuList, NavigationMenuItem } from '@/components/ui/navigation-menu';
import { Link as ScrollLink, animateScroll as scroll } from 'react-scroll';

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string;
    meta_description: string | null;
    meta_keywords: string | null;
    featured_image: string | null;
    page_layout: string;
    published_at: string | null;
    is_featured: boolean;
}

interface Profile {
    page_title: string;
    logo_text: string;
    logo_type: string;
    logo_icon: string;
    logo_color: string;
    navbar_items: any[];
    hire_me_enabled: boolean;
    hire_me_text: string;
    hire_me_url: string;
}

interface Props {
    page: Page;
    profile: Profile;
}

export default function PageView({ page, profile }: Props) {
    const [showScrollTop, setShowScrollTop] = useState(false);
    const { scrollY } = useScroll();

    // Navigation animation values
    const navbarBackground = useTransform(
        scrollY,
        [0, 100],
        ["rgba(255, 255, 255, 0.8)", "rgba(255, 255, 255, 0.95)"]
    );
    const navbarHeight = useTransform(scrollY, [0, 100], ["80px", "64px"]);
    const navbarShadow = useTransform(
        scrollY,
        [0, 100],
        ["0 0 0 rgba(0, 0, 0, 0)", "0 4px 20px rgba(0, 0, 0, 0.1)"]
    );

    // Logo configuration
    const logoText = profile?.logo_text || 'Portfolio';
    const logoType = profile?.logo_type || 'text_only';
    const logoIcon = profile?.logo_icon || 'P';
    const logoColor = profile?.logo_color || '#20B2AA';

    // Parse logo icon to determine type
    const logoIconType = logoIcon?.startsWith('<svg') ? 'svg' : 'letter';

    // Navigation items
    const defaultNavItems = [
        { title: 'Home', href: '/' },
        { title: 'Services', href: '/#services' },
        { title: 'Projects', href: '/#works' },
        { title: 'Skills', href: '/#skills' },
        { title: 'Resume', href: '/#resume' },
        { title: 'Testimonials', href: '/#testimonials' },
        { title: 'Contact', href: '/#contact' }
    ];

    let navItems = defaultNavItems;
    if (profile?.navbar_items && Array.isArray(profile.navbar_items)) {
        navItems = profile.navbar_items.map(item => ({
            title: item.title,
            href: item.href.startsWith('#') ? `/${item.href}` : item.href
        }));
    }

    // Hire me button settings
    const hireMeText = profile?.hire_me_text || 'Hire Me';
    const hireMeUrl = profile?.hire_me_url || '/#contact';
    const hireMeEnabled = profile?.hire_me_enabled ?? true;

    // Show/hide scroll to top button
    useEffect(() => {
        const handleScrollVisibility = () => {
            if (window.scrollY > 500) {
                setShowScrollTop(true);
            } else {
                setShowScrollTop(false);
            }
        };

        window.addEventListener('scroll', handleScrollVisibility);
        return () => window.removeEventListener('scroll', handleScrollVisibility);
    }, []);

    const scrollToTop = () => {
        scroll.scrollToTop({
            duration: 500,
            smooth: true
        });
    };

    return (
        <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
            <Head
                title={`${page.title} - ${profile.page_title}`}
                description={page.meta_description || `Read ${page.title} on ${profile.page_title}`}
            >
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
                {page.meta_keywords && (
                    <meta name="keywords" content={page.meta_keywords} />
                )}
                <meta property="og:title" content={page.title} />
                <meta property="og:description" content={page.meta_description || `Read ${page.title} on ${profile.page_title}`} />
                {page.featured_image && (
                    <meta property="og:image" content={page.featured_image} />
                )}
                <meta property="og:type" content="article" />
            </Head>

            <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                {/* Navigation */}
                <motion.nav
                    style={{
                        backgroundColor: navbarBackground,
                        height: navbarHeight,
                        boxShadow: navbarShadow,
                    }}
                    className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 sm:px-6 md:px-8 py-4 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 transition-all duration-300 dark:bg-transparent"
                >
                    <motion.div
                        className="absolute inset-0 z-[-1] dark:bg-gray-900/95 backdrop-blur-sm"
                    />

                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={scrollToTop}
                        className="cursor-pointer"
                    >
                        <DynamicLogo
                            logoText={logoText}
                            logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                            logoIcon={logoIcon}
                            logoIconType={logoIconType as 'letter' | 'svg'}
                            logoColor={logoColor}
                        />
                    </motion.div>

                    {/* Desktop Navigation */}
                    <div className="hidden md:block">
                        <NavigationMenu>
                            <NavigationMenuList className="flex gap-4 lg:gap-8">
                                {navItems.map((item, index) => (
                                    <NavigationMenuItem key={item.title}>
                                        <motion.div
                                            initial={{ opacity: 0, y: -20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: index * 0.1 }}
                                        >
                                            <motion.div whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 400 }}>
                                                <a
                                                    href={item.href}
                                                    className="text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] cursor-pointer font-medium text-sm transition-colors px-2 py-1.5 block"
                                                >
                                                    {item.title}
                                                </a>
                                            </motion.div>
                                        </motion.div>
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    <motion.div className="flex items-center space-x-2">
                        <ThemeToggle />
                        {hireMeEnabled && (
                            <motion.a
                                href={hireMeUrl}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.5 }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="hidden sm:inline-flex items-center px-4 py-2 bg-[#20B2AA] text-white rounded-full font-medium text-sm hover:bg-[#1a9994] transition-colors shadow-lg hover:shadow-xl"
                            >
                                {hireMeText}
                            </motion.a>
                        )}
                    </motion.div>
                </motion.nav>

                <main className="pt-20">
                    {/* Hero Section */}
                    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                {page.is_featured && (
                                    <motion.div
                                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#20B2AA] text-white mb-4"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <Tag className="w-4 h-4 mr-1" />
                                        Featured Page
                                    </motion.div>
                                )}

                                <motion.h1
                                    className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    {page.title}
                                </motion.h1>

                                {page.meta_description && (
                                    <motion.p
                                        className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        {page.meta_description}
                                    </motion.p>
                                )}

                                <motion.div
                                    className="flex items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    {page.published_at && (
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4" />
                                            <span>Published {page.published_at}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        <span>{profile.page_title}</span>
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Featured Image */}
                    {page.featured_image && (
                        <section className="py-8">
                            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                                    <img
                                        src={page.featured_image}
                                        alt={page.title}
                                        className="w-full h-64 md:h-96 object-cover"
                                    />
                                </div>
                            </div>
                        </section>
                    )}

                    {/* Content */}
                    <section className="py-16">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="prose prose-lg max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-[#20B2AA] prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 dark:prose-strong:text-white prose-code:text-[#20B2AA] prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-blockquote:border-l-[#20B2AA] prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300"
                                dangerouslySetInnerHTML={{ __html: page.content }}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 }}
                            />
                        </div>
                    </section>

                    {/* Back to Pages */}
                    <section className="py-8 border-t border-gray-200 dark:border-gray-700">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.8 }}
                            >
                                <a
                                    href="/"
                                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-[#20B2AA] hover:bg-[#1a9994] transition-colors duration-200"
                                >
                                    ← Back to Home
                                </a>
                            </motion.div>
                        </div>
                    </section>
                </main>

                {/* Scroll to Top Button */}
                {showScrollTop && (
                    <motion.button
                        onClick={scrollToTop}
                        className="fixed bottom-8 right-8 z-40 p-3 bg-[#20B2AA] text-white rounded-full shadow-lg hover:bg-[#1a9994] transition-colors"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <ArrowUp className="w-5 h-5" />
                    </motion.button>
                )}

                {/* Footer */}
                <footer className="bg-gray-900 dark:bg-gray-950 text-white py-12">
                    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div>
                                <DynamicLogo
                                    logoText={logoText}
                                    logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                                    logoIcon={logoIcon}
                                    logoIconType={logoIconType as 'letter' | 'svg'}
                                    logoColor={logoColor}
                                />
                                <p className="mt-4 text-gray-400">
                                    Creating exceptional digital experiences through innovative design and development solutions.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                                <ul className="space-y-2">
                                    {navItems.slice(0, 4).map((item) => (
                                        <li key={item.title}>
                                            <a
                                                href={item.href}
                                                className="text-gray-400 hover:text-[#20B2AA] transition-colors"
                                            >
                                                {item.title}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">Get in Touch</h3>
                                <p className="text-gray-400 mb-4">
                                    Ready to start your next project? Let's create something amazing together.
                                </p>
                                {hireMeEnabled && (
                                    <a
                                        href={hireMeUrl}
                                        className="inline-flex items-center px-6 py-3 bg-[#20B2AA] text-white rounded-full font-medium hover:bg-[#1a9994] transition-colors"
                                    >
                                        {hireMeText}
                                    </a>
                                )}
                            </div>
                        </div>

                        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
                            <p className="text-gray-400">
                                © 2025 {profile.page_title}. All rights reserved.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </ThemeProvider>
    );
}
