import { Head, <PERSON> } from '@inertiajs/react';
import { Navbar } from '@/components/navbar';
import { Footer } from '@/components/footer';
import { Calendar, ArrowRight, Star } from 'lucide-react';

interface Page {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    featured_image: string | null;
    published_at: string | null;
    is_featured: boolean;
    url: string;
}

interface Profile {
    page_title: string;
    logo_text: string;
    logo_type: string;
    logo_icon: string;
    logo_color: string;
    navbar_items: any[];
    hire_me_enabled: boolean;
    hire_me_text: string;
    hire_me_url: string;
}

interface Props {
    pages: Page[];
    profile: Profile;
}

export default function PagesIndex({ pages, profile }: Props) {
    const featuredPages = pages.filter(page => page.is_featured);
    const regularPages = pages.filter(page => !page.is_featured);

    return (
        <>
            <Head 
                title={`Pages - ${profile.page_title}`}
                description={`Browse all pages on ${profile.page_title}`}
            />

            <div className="min-h-screen bg-white dark:bg-gray-900">
                <Navbar profile={profile} />
                
                <main className="pt-20">
                    {/* Hero Section */}
                    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                                    All Pages
                                </h1>
                                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Explore our collection of pages and articles
                                </p>
                            </div>
                        </div>
                    </section>

                    {/* Featured Pages */}
                    {featuredPages.length > 0 && (
                        <section className="py-16">
                            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                <div className="flex items-center gap-2 mb-8">
                                    <Star className="w-6 h-6 text-yellow-500 fill-current" />
                                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                                        Featured Pages
                                    </h2>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {featuredPages.map((page) => (
                                        <PageCard key={page.id} page={page} featured />
                                    ))}
                                </div>
                            </div>
                        </section>
                    )}

                    {/* All Pages */}
                    <section className={`py-16 ${featuredPages.length > 0 ? 'border-t border-gray-200 dark:border-gray-700' : ''}`}>
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                {featuredPages.length > 0 ? 'All Pages' : 'Pages'}
                            </h2>
                            
                            {pages.length === 0 ? (
                                <div className="text-center py-16">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                        <Calendar className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                        No pages available
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-400">
                                        Check back later for new content.
                                    </p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {(featuredPages.length > 0 ? regularPages : pages).map((page) => (
                                        <PageCard key={page.id} page={page} />
                                    ))}
                                </div>
                            )}
                        </div>
                    </section>
                </main>

                <Footer />
            </div>
        </>
    );
}

function PageCard({ page, featured = false }: { page: Page; featured?: boolean }) {
    return (
        <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
            featured ? 'ring-2 ring-yellow-400' : ''
        }`}>
            {page.featured_image && (
                <div className="relative h-48 overflow-hidden">
                    <img
                        src={page.featured_image}
                        alt={page.title}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    />
                    {featured && (
                        <div className="absolute top-4 right-4">
                            <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                                <Star className="w-3 h-3 fill-current" />
                                Featured
                            </div>
                        </div>
                    )}
                </div>
            )}
            
            <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                    {page.published_at && (
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                            <Calendar className="w-4 h-4" />
                            <span>{page.published_at}</span>
                        </div>
                    )}
                    {featured && !page.featured_image && (
                        <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                            <Star className="w-3 h-3 fill-current" />
                            Featured
                        </div>
                    )}
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2">
                    {page.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                    {page.excerpt}
                </p>
                
                <Link
                    href={page.url}
                    className="inline-flex items-center gap-2 text-[#20B2AA] hover:text-[#1a9994] font-medium transition-colors duration-200"
                >
                    Read More
                    <ArrowRight className="w-4 h-4" />
                </Link>
            </div>
        </div>
    );
}
