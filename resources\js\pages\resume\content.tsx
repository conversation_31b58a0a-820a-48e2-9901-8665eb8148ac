import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { FileText, Upload, Download, Trash2, Save } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useForm } from '@inertiajs/react';
import { toast } from 'sonner';
import axios from 'axios';

interface ResumeContent {
    id: number;
    section_badge: string;
    section_title: string;
    section_description: string;
    experience_title: string;
    education_title: string;
    download_button_text: string;
    resume_file_path: string | null;
    experience_display_limit: number;
    education_display_limit: number;
}

interface FormData {
    section_badge: string;
    section_title: string;
    section_description: string;
    experience_title: string;
    education_title: string;
    download_button_text: string;
    experience_display_limit: number | string;
    education_display_limit: number | string;
}

export default function ResumeContentManagement() {
    const [resumeContent, setResumeContent] = useState<ResumeContent | null>(null);
    const [loading, setLoading] = useState(true);
    const [uploading, setUploading] = useState(false);

    const form = useForm<FormData>({
        section_badge: '',
        section_title: '',
        section_description: '',
        experience_title: '',
        education_title: '',
        download_button_text: '',
        experience_display_limit: 3,
        education_display_limit: 3,
    });

    // Fetch resume content
    const fetchResumeContent = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/admin/resume-content');
            const data = response.data;
            setResumeContent(data);
            form.setData({
                section_badge: data.section_badge,
                section_title: data.section_title,
                section_description: data.section_description,
                experience_title: data.experience_title,
                education_title: data.education_title,
                download_button_text: data.download_button_text,
                experience_display_limit: data.experience_display_limit || 3,
                education_display_limit: data.education_display_limit || 3,
            });
        } catch (error) {
            toast.error('Failed to fetch resume content');
            console.error('Error fetching resume content:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchResumeContent();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            // Ensure display limits are numbers before submitting
            const submitData = {
                ...form.data,
                experience_display_limit: typeof form.data.experience_display_limit === 'string'
                    ? parseInt(form.data.experience_display_limit) || 1
                    : form.data.experience_display_limit,
                education_display_limit: typeof form.data.education_display_limit === 'string'
                    ? parseInt(form.data.education_display_limit) || 1
                    : form.data.education_display_limit,
            };

            const response = await axios.put('/admin/resume-content', submitData);
            setResumeContent(response.data);
            toast.success('Resume content updated successfully');
        } catch (error) {
            toast.error('Failed to update resume content');
            console.error('Error updating resume content:', error);
        }
    };

    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        if (file.type !== 'application/pdf') {
            toast.error('Please upload a PDF file');
            return;
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB
            toast.error('File size must be less than 10MB');
            return;
        }

        const formData = new FormData();
        formData.append('resume', file);

        try {
            setUploading(true);
            const response = await axios.post('/admin/resume-content/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            
            // Update the resume content with new file path
            if (resumeContent) {
                setResumeContent({
                    ...resumeContent,
                    resume_file_path: response.data.file_path
                });
            }
            
            toast.success('Resume uploaded successfully');
        } catch (error) {
            toast.error('Failed to upload resume');
            console.error('Error uploading resume:', error);
        } finally {
            setUploading(false);
        }
    };

    const handleDeleteResume = async () => {
        try {
            await axios.delete('/admin/resume-content/file');
            
            if (resumeContent) {
                setResumeContent({
                    ...resumeContent,
                    resume_file_path: null
                });
            }
            
            toast.success('Resume deleted successfully');
        } catch (error) {
            toast.error('Failed to delete resume');
            console.error('Error deleting resume:', error);
        }
    };

    const handleDownloadResume = () => {
        if (resumeContent?.resume_file_path) {
            window.open('/download-resume', '_blank');
        }
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h2 className="text-lg font-medium text-gray-900">Resume Content</h2>
                        <p className="text-sm text-gray-500">Loading resume content...</p>
                    </div>
                </div>
                <Card className="border border-gray-200 p-6">
                    <div className="animate-pulse space-y-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-lg font-medium text-gray-900">Resume Content Management</h2>
                    <p className="text-sm text-gray-500">Manage resume section content and file</p>
                </div>
            </div>

            {/* Content Management Form */}
            <Card className="border border-gray-200">
                <div className="px-6 py-4 bg-gradient-to-r from-[#E6F7F6] to-white border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                        <div className="p-1 rounded-lg bg-[#20B2AA]/10">
                            <FileText className="w-4 h-4 text-[#20B2AA]" />
                        </div>
                        Section Content
                    </h3>
                </div>
                
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <Label htmlFor="section_badge" className="text-sm font-medium">
                                Section Badge
                                <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                                id="section_badge"
                                value={form.data.section_badge}
                                onChange={e => form.setData('section_badge', e.target.value)}
                                placeholder="e.g. Resume"
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="section_title" className="text-sm font-medium">
                                Section Title
                                <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                                id="section_title"
                                value={form.data.section_title}
                                onChange={e => form.setData('section_title', e.target.value)}
                                placeholder="e.g. Experience & Education"
                                required
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="section_description" className="text-sm font-medium">
                            Section Description
                            <span className="text-red-500 ml-1">*</span>
                        </Label>
                        <Textarea
                            id="section_description"
                            value={form.data.section_description}
                            onChange={e => form.setData('section_description', e.target.value)}
                            placeholder="e.g. My professional journey and academic background"
                            rows={3}
                            required
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <Label htmlFor="experience_title" className="text-sm font-medium">
                                Experience Section Title
                                <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                                id="experience_title"
                                value={form.data.experience_title}
                                onChange={e => form.setData('experience_title', e.target.value)}
                                placeholder="e.g. Experience"
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="education_title" className="text-sm font-medium">
                                Education Section Title
                                <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                                id="education_title"
                                value={form.data.education_title}
                                onChange={e => form.setData('education_title', e.target.value)}
                                placeholder="e.g. Education"
                                required
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="download_button_text" className="text-sm font-medium">
                            Download Button Text
                            <span className="text-red-500 ml-1">*</span>
                        </Label>
                        <Input
                            id="download_button_text"
                            value={form.data.download_button_text}
                            onChange={e => form.setData('download_button_text', e.target.value)}
                            placeholder="e.g. Download Full Resume"
                            required
                        />
                    </div>

                    {/* Display Limits Section */}
                    <div className="border-t border-gray-200 pt-6">
                        <h4 className="text-sm font-medium text-gray-900 mb-4">Homepage Display Limits</h4>
                        <p className="text-sm text-gray-500 mb-4">
                            Control how many experience and education entries are shown on the homepage.
                            This helps maintain a clean layout while allowing unlimited entries in the admin panel.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="experience_display_limit" className="text-sm font-medium">
                                    Experience Display Limit
                                    <span className="text-red-500 ml-1">*</span>
                                </Label>
                                <Input
                                    id="experience_display_limit"
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={form.data.experience_display_limit.toString()}
                                    onChange={e => {
                                        const value = e.target.value;
                                        if (value === '') {
                                            // Allow empty field while typing
                                            form.setData('experience_display_limit', '' as any);
                                        } else {
                                            const numValue = parseInt(value);
                                            if (!isNaN(numValue) && numValue >= 1 && numValue <= 10) {
                                                form.setData('experience_display_limit', numValue);
                                            }
                                        }
                                    }}
                                    onBlur={e => {
                                        // Ensure valid value on blur
                                        const value = e.target.value;
                                        if (value === '' || isNaN(parseInt(value))) {
                                            form.setData('experience_display_limit', 1);
                                        }
                                    }}
                                    placeholder="3"
                                    required
                                />
                                <p className="text-xs text-gray-500">
                                    Maximum number of experience entries to show on homepage (1-10)
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="education_display_limit" className="text-sm font-medium">
                                    Education Display Limit
                                    <span className="text-red-500 ml-1">*</span>
                                </Label>
                                <Input
                                    id="education_display_limit"
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={form.data.education_display_limit.toString()}
                                    onChange={e => {
                                        const value = e.target.value;
                                        if (value === '') {
                                            // Allow empty field while typing
                                            form.setData('education_display_limit', '' as any);
                                        } else {
                                            const numValue = parseInt(value);
                                            if (!isNaN(numValue) && numValue >= 1 && numValue <= 10) {
                                                form.setData('education_display_limit', numValue);
                                            }
                                        }
                                    }}
                                    onBlur={e => {
                                        // Ensure valid value on blur
                                        const value = e.target.value;
                                        if (value === '' || isNaN(parseInt(value))) {
                                            form.setData('education_display_limit', 1);
                                        }
                                    }}
                                    placeholder="3"
                                    required
                                />
                                <p className="text-xs text-gray-500">
                                    Maximum number of education entries to show on homepage (1-10)
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <Button 
                            type="submit" 
                            className="bg-[#20B2AA] hover:bg-[#1a9994] text-white"
                            disabled={form.processing}
                        >
                            <Save className="w-4 h-4 mr-2" />
                            {form.processing ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </form>
            </Card>

            {/* Resume File Management */}
            <Card className="border border-gray-200">
                <div className="px-6 py-4 bg-gradient-to-r from-[#E6F7F6] to-white border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                        <div className="p-1 rounded-lg bg-[#20B2AA]/10">
                            <Upload className="w-4 h-4 text-[#20B2AA]" />
                        </div>
                        Resume File Management
                    </h3>
                </div>
                
                <div className="p-6 space-y-4">
                    {resumeContent?.resume_file_path ? (
                        <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center gap-3">
                                <FileText className="w-5 h-5 text-green-600" />
                                <div>
                                    <p className="text-sm font-medium text-green-900">Resume file uploaded</p>
                                    <p className="text-xs text-green-700">File is ready for download</p>
                                </div>
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    onClick={handleDownloadResume}
                                    variant="outline"
                                    size="sm"
                                    className="text-green-700 border-green-300 hover:bg-green-50"
                                >
                                    <Download className="w-4 h-4 mr-1" />
                                    Download
                                </Button>
                                <Button
                                    onClick={handleDeleteResume}
                                    variant="outline"
                                    size="sm"
                                    className="text-red-700 border-red-300 hover:bg-red-50"
                                >
                                    <Trash2 className="w-4 h-4 mr-1" />
                                    Delete
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-sm font-medium text-gray-900 mb-1">No resume file uploaded</h3>
                            <p className="text-sm text-gray-500 mb-4">Upload a PDF file to enable resume downloads</p>
                        </div>
                    )}

                    <div className="flex justify-center">
                        <label className="cursor-pointer">
                            <input
                                type="file"
                                accept=".pdf"
                                onChange={handleFileUpload}
                                className="hidden"
                                disabled={uploading}
                            />
                            <Button
                                type="button"
                                variant="outline"
                                className="border-[#20B2AA] text-[#20B2AA] hover:bg-[#E6F7F6]"
                                disabled={uploading}
                            >
                                <Upload className="w-4 h-4 mr-2" />
                                {uploading ? 'Uploading...' : resumeContent?.resume_file_path ? 'Replace Resume' : 'Upload Resume'}
                            </Button>
                        </label>
                    </div>
                    
                    <p className="text-xs text-gray-500 text-center">
                        Supported format: PDF (max 10MB)
                    </p>
                </div>
            </Card>
        </div>
    );
}
