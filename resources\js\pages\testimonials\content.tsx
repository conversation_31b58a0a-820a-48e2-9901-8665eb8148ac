import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { MessageSquare, Save } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useForm } from '@inertiajs/react';
import { toast } from 'sonner';
import axios from 'axios';

interface TestimonialsContent {
    id: number;
    section_badge: string;
    section_title: string;
    section_description: string;
}

interface FormData {
    section_badge: string;
    section_title: string;
    section_description: string;
}

export default function TestimonialsContentManagement() {
    const [testimonialsContent, setTestimonialsContent] = useState<TestimonialsContent | null>(null);
    const [loading, setLoading] = useState(true);

    const form = useForm<FormData>({
        section_badge: '',
        section_title: '',
        section_description: '',
    });

    // Fetch testimonials content
    const fetchTestimonialsContent = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/admin/testimonials-content');
            const data = response.data;
            setTestimonialsContent(data);
            form.setData({
                section_badge: data.section_badge,
                section_title: data.section_title,
                section_description: data.section_description,
            });
        } catch (error) {
            toast.error('Failed to fetch testimonials content');
            console.error('Error fetching testimonials content:', error);
        } finally {
            setLoading(false);
        }
    };

    // Update testimonials content
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            const response = await axios.put('/admin/testimonials-content', form.data);
            setTestimonialsContent(response.data);
            toast.success('Testimonials content updated successfully');
        } catch (error) {
            toast.error('Failed to update testimonials content');
            console.error('Error updating testimonials content:', error);
        }
    };

    useEffect(() => {
        fetchTestimonialsContent();
    }, []);

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <Card className="border border-gray-200">
                        <div className="px-6 py-4 bg-gradient-to-r from-[#E6F7F6] to-white border-b border-gray-200">
                            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-10 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-10 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-20 bg-gray-200 rounded"></div>
                        </div>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-lg font-medium text-gray-900">Testimonials Content Management</h2>
                    <p className="text-sm text-gray-500">Manage testimonials section content</p>
                </div>
            </div>

            {/* Content Management Form */}
            <Card className="border border-gray-200">
                <div className="px-6 py-4 bg-gradient-to-r from-[#E6F7F6] to-white border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                        <div className="p-1 rounded-lg bg-[#20B2AA]/10">
                            <MessageSquare className="w-4 h-4 text-[#20B2AA]" />
                        </div>
                        Section Content
                    </h3>
                </div>
                
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    {/* Section Badge */}
                    <div className="space-y-2">
                        <Label htmlFor="section_badge" className="text-sm font-medium text-gray-700">
                            Section Badge
                        </Label>
                        <Input
                            id="section_badge"
                            type="text"
                            value={form.data.section_badge}
                            onChange={(e) => form.setData('section_badge', e.target.value)}
                            placeholder="e.g., Testimonials"
                            className="border-gray-300 focus:border-[#20B2AA] focus:ring-[#20B2AA]"
                            required
                        />
                        <p className="text-xs text-gray-500">
                            The small badge text that appears above the section title
                        </p>
                    </div>

                    {/* Section Title */}
                    <div className="space-y-2">
                        <Label htmlFor="section_title" className="text-sm font-medium text-gray-700">
                            Section Title
                        </Label>
                        <Input
                            id="section_title"
                            type="text"
                            value={form.data.section_title}
                            onChange={(e) => form.setData('section_title', e.target.value)}
                            placeholder="e.g., What Clients Say"
                            className="border-gray-300 focus:border-[#20B2AA] focus:ring-[#20B2AA]"
                            required
                        />
                        <p className="text-xs text-gray-500">
                            The main heading for the testimonials section
                        </p>
                    </div>

                    {/* Section Description */}
                    <div className="space-y-2">
                        <Label htmlFor="section_description" className="text-sm font-medium text-gray-700">
                            Section Description
                        </Label>
                        <Textarea
                            id="section_description"
                            value={form.data.section_description}
                            onChange={(e) => form.setData('section_description', e.target.value)}
                            placeholder="e.g., Feedback from clients who have experienced working with me."
                            className="border-gray-300 focus:border-[#20B2AA] focus:ring-[#20B2AA] min-h-[80px]"
                            required
                        />
                        <p className="text-xs text-gray-500">
                            A brief description that appears below the section title
                        </p>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end pt-4 border-t border-gray-200">
                        <Button
                            type="submit"
                            className="bg-[#20B2AA] hover:bg-[#1a9b94] text-white"
                            disabled={form.processing}
                        >
                            <Save className="w-4 h-4 mr-2" />
                            {form.processing ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </form>
            </Card>
        </div>
    );
}
