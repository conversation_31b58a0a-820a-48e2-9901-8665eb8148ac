import { useForm, router } from '@inertiajs/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Plus, Search, GripVertical, Edit2, Trash2, MoreHorizontal, ArrowUp, ArrowDown, Save, Star } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import DeleteConfirmation from '@/components/ui/delete-confirmation';

interface Testimonial {
    id: number;
    name: string;
    position: string;
    company: string;
    quote: string;
    rating: number;
    image: string | null;
    order: number;
}

interface Props {
    testimonials: Testimonial[];
}

interface FormData {
    name: string;
    position: string;
    company: string;
    quote: string;
    rating: number;
    image: File | null;
}

export default function TestimonialsManagement({ testimonials: initialTestimonials }: Props) {
    const [testimonials, setTestimonials] = useState<Testimonial[]>(initialTestimonials);
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [editingTestimonial, setEditingTestimonial] = useState<Testimonial | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
    const [testimonialToDelete, setTestimonialToDelete] = useState<Testimonial | null>(null);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    const form = useForm<FormData>({
        name: '',
        position: '',
        company: '',
        quote: '',
        rating: 5,
        image: null,
    });

    const editForm = useForm<FormData>({
        name: '',
        position: '',
        company: '',
        quote: '',
        rating: 5,
        image: null,
    });

    // Filter testimonials based on search query
    const filteredTestimonials = testimonials.filter(testimonial => 
        testimonial.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        testimonial.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
        testimonial.position.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.post(route('admin.testimonials.store'), {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                form.reset();
                toast.success('Testimonial created successfully');
            },
        });
    };

    const handleEdit = (testimonial: Testimonial) => {
        setEditingTestimonial(testimonial);
        editForm.setData({
            name: testimonial.name,
            position: testimonial.position,
            company: testimonial.company,
            quote: testimonial.quote,
            rating: testimonial.rating,
            image: null,
        });
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingTestimonial) return;

        editForm.put(route('admin.testimonials.update', { testimonial: editingTestimonial.id }), {
            onSuccess: () => {
                setEditingTestimonial(null);
                editForm.reset();
                toast.success('Testimonial updated successfully');
            },
        });
    };

    const confirmDelete = (testimonial: Testimonial) => {
        setTestimonialToDelete(testimonial);
        setDeleteConfirmOpen(true);
    };

    const handleDelete = () => {
        if (!testimonialToDelete) return;

        router.delete(route('admin.testimonials.destroy', { testimonial: testimonialToDelete.id }), {
            onSuccess: () => {
                setDeleteConfirmOpen(false);
                setTestimonialToDelete(null);
                toast.success('Testimonial deleted successfully');

                // Reload the page to get fresh data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            },
            onError: () => {
                toast.error('Failed to delete testimonial');
                setDeleteConfirmOpen(false);
                setTestimonialToDelete(null);
            }
        });
    };

    const handleMoveUp = (index: number) => {
        if (index === 0) return;
        const newTestimonials = [...testimonials];
        [newTestimonials[index], newTestimonials[index - 1]] = [newTestimonials[index - 1], newTestimonials[index]];
        // Update order values
        newTestimonials[index].order = index + 1;
        newTestimonials[index - 1].order = index;
        setTestimonials(newTestimonials);
        setHasUnsavedChanges(true);
    };

    const handleMoveDown = (index: number) => {
        if (index === testimonials.length - 1) return;
        const newTestimonials = [...testimonials];
        [newTestimonials[index], newTestimonials[index + 1]] = [newTestimonials[index + 1], newTestimonials[index]];
        // Update order values
        newTestimonials[index].order = index + 1;
        newTestimonials[index + 1].order = index + 2;
        setTestimonials(newTestimonials);
        setHasUnsavedChanges(true);
    };

    const handleSaveOrder = async () => {
        const orderData = testimonials.map((item, index) => ({
            id: item.id,
            order: index + 1
        }));

        try {
            const response = await fetch(route('admin.testimonials.reorder'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ testimonials: orderData }),
            });

            if (response.ok) {
                setHasUnsavedChanges(false);
                toast.success('Testimonials order saved successfully');
            } else {
                throw new Error('Failed to save order');
            }
        } catch (error) {
            toast.error('Failed to save testimonials order');
            console.error('Error saving testimonials order:', error);
        }
    };

    const handleDragEnd = (result: DropResult) => {
        if (!result.destination) return;

        const items = Array.from(testimonials);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        // Update order values
        const updatedItems = items.map((item, index) => ({
            ...item,
            order: index + 1
        }));

        setTestimonials(updatedItems);
        setHasUnsavedChanges(true);
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-lg font-medium text-gray-900">Testimonials Management</h2>
                    <p className="text-sm text-gray-500">Manage client testimonials and reviews</p>
                </div>
                <div className="flex items-center gap-4">
                    {hasUnsavedChanges && (
                        <Button
                            onClick={handleSaveOrder}
                            className="bg-[#20B2AA] hover:bg-[#1a9b94] text-white"
                        >
                            <Save className="w-4 h-4 mr-2" />
                            Save Order
                        </Button>
                    )}
                    <Button onClick={() => setIsAddDialogOpen(true)}>
                        <Plus className="w-4 h-4 mr-2" />
                        Add New Testimonial
                    </Button>
                </div>
            </div>

            {/* Search */}
            <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                    placeholder="Search testimonials..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                />
            </div>

            {/* Testimonials List */}
            <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="testimonials">
                    {(provided) => (
                        <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className="space-y-4"
                        >
                            {testimonials.map((testimonial, index) => (
                                <Draggable
                                    key={testimonial.id}
                                    draggableId={testimonial.id.toString()}
                                    index={index}
                                >
                                    {(provided, snapshot) => (
                                        <motion.div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.3 }}
                                            className={cn(
                                                "bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow",
                                                snapshot.isDragging && "shadow-lg ring-2 ring-blue-500 ring-opacity-50"
                                            )}
                                        >
                                            <div className="flex items-start gap-4">
                                                <div
                                                    {...provided.dragHandleProps}
                                                    className="mt-2 text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
                                                >
                                                    <GripVertical className="w-5 h-5" />
                                                </div>

                                                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 flex items-center justify-center text-white text-xl font-semibold flex-shrink-0">
                                                    {testimonial.name.charAt(0)}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <h3 className="text-base font-medium text-gray-900">{testimonial.name}</h3>
                                                    </div>
                                                    <p className="text-sm text-gray-600 mb-2">
                                                        {testimonial.position} at {testimonial.company}
                                                    </p>
                                                    <div className="flex items-center gap-1 mb-3">
                                                        {Array.from({ length: 5 }).map((_, i) => (
                                                            <Star 
                                                                key={i}
                                                                className={cn(
                                                                    "w-4 h-4",
                                                                    i < testimonial.rating ? "text-amber-400 fill-current" : "text-gray-300"
                                                                )}
                                                            />
                                                        ))}
                                                    </div>
                                                    <p className="text-gray-600 text-sm line-clamp-2">{testimonial.quote}</p>
                                                </div>

                                                <div className="flex items-center gap-2">
                                                    {index !== 0 && (
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 text-gray-500 hover:text-[#20B2AA] hover:bg-[#E6F7F6]"
                                                            onClick={() => handleMoveUp(index)}
                                                        >
                                                            <ArrowUp className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                    {index !== testimonials.length - 1 && (
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 text-gray-500 hover:text-[#20B2AA] hover:bg-[#E6F7F6]"
                                                            onClick={() => handleMoveDown(index)}
                                                        >
                                                            <ArrowDown className="h-4 w-4" />
                                                        </Button>
                                                    )}

                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="icon" className="h-8 w-8">
                                                                <MoreHorizontal className="w-4 h-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem onClick={() => handleEdit(testimonial)}>
                                                                <Edit2 className="w-4 h-4 mr-2" />
                                                                Edit
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem 
                                                                onClick={() => confirmDelete(testimonial)}
                                                                className="text-red-600"
                                                            >
                                                                <Trash2 className="w-4 h-4 mr-2" />
                                                                Delete
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </div>
                                        </motion.div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </DragDropContext>

            {/* Add Dialog */}
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Add New Testimonial</DialogTitle>
                        <DialogDescription>
                            Create a new client testimonial to showcase your work.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-1 block">Name</label>
                                <Input
                                    value={form.data.name}
                                    onChange={(e) => form.setData('name', e.target.value)}
                                    placeholder="Client name"
                                    required
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-1 block">Position</label>
                                <Input
                                    value={form.data.position}
                                    onChange={(e) => form.setData('position', e.target.value)}
                                    placeholder="Job title"
                                    required
                                />
                            </div>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Company</label>
                            <Input
                                value={form.data.company}
                                onChange={(e) => form.setData('company', e.target.value)}
                                placeholder="Company name"
                                required
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Testimonial</label>
                            <Textarea
                                value={form.data.quote}
                                onChange={(e) => form.setData('quote', e.target.value)}
                                placeholder="Client testimonial..."
                                className="min-h-[100px]"
                                required
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Rating</label>
                            <select
                                value={form.data.rating}
                                onChange={(e) => form.setData('rating', parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                {[1, 2, 3, 4, 5].map(rating => (
                                    <option key={rating} value={rating}>{rating} Star{rating !== 1 ? 's' : ''}</option>
                                ))}
                            </select>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {form.processing ? 'Creating...' : 'Create Testimonial'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Edit Dialog */}
            <Dialog open={!!editingTestimonial} onOpenChange={() => setEditingTestimonial(null)}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit Testimonial</DialogTitle>
                        <DialogDescription>
                            Update the testimonial information.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-1 block">Name</label>
                                <Input
                                    value={editForm.data.name}
                                    onChange={(e) => editForm.setData('name', e.target.value)}
                                    placeholder="Client name"
                                    required
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-1 block">Position</label>
                                <Input
                                    value={editForm.data.position}
                                    onChange={(e) => editForm.setData('position', e.target.value)}
                                    placeholder="Job title"
                                    required
                                />
                            </div>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Company</label>
                            <Input
                                value={editForm.data.company}
                                onChange={(e) => editForm.setData('company', e.target.value)}
                                placeholder="Company name"
                                required
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Testimonial</label>
                            <Textarea
                                value={editForm.data.quote}
                                onChange={(e) => editForm.setData('quote', e.target.value)}
                                placeholder="Client testimonial..."
                                className="min-h-[100px]"
                                required
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 mb-1 block">Rating</label>
                            <select
                                value={editForm.data.rating}
                                onChange={(e) => editForm.setData('rating', parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                {[1, 2, 3, 4, 5].map(rating => (
                                    <option key={rating} value={rating}>{rating} Star{rating !== 1 ? 's' : ''}</option>
                                ))}
                            </select>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setEditingTestimonial(null)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={editForm.processing}>
                                {editForm.processing ? 'Updating...' : 'Update Testimonial'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation */}
            <DeleteConfirmation
                isOpen={deleteConfirmOpen}
                onClose={() => {
                    setDeleteConfirmOpen(false);
                    setTestimonialToDelete(null);
                }}
                onConfirm={handleDelete}
                title="Delete Testimonial"
                description={`Are you sure you want to delete the testimonial from ${testimonialToDelete?.name}? This action cannot be undone.`}
            />
        </div>
    );
}
