export interface Experience {
    id: number;
    title: string;
    company: string;
    location: string;
    start_date: string;
    end_date: string | null;
    is_current: boolean;
    description: string;
    order: number;
    is_visible: boolean;
    formatted_period?: string;
}

export interface Education {
    id: number;
    degree: string;
    institution: string;
    location: string;
    start_date: string;
    end_date: string | null;
    description: string | null;
    order: number;
    is_visible: boolean;
    formatted_period?: string;
}