const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.profile":{"uri":"admin\/profile","methods":["GET","HEAD"]},"admin.services.index":{"uri":"admin\/services","methods":["GET","HEAD"]},"admin.services.create":{"uri":"admin\/services\/create","methods":["GET","HEAD"]},"admin.services.store":{"uri":"admin\/services","methods":["POST"]},"admin.services.edit":{"uri":"admin\/services\/{service}\/edit","methods":["GET","HEAD"],"parameters":["service"],"bindings":{"service":"id"}},"admin.services.update":{"uri":"admin\/services\/{service}","methods":["PUT"],"parameters":["service"],"bindings":{"service":"id"}},"admin.services.destroy":{"uri":"admin\/services\/{service}","methods":["DELETE"],"parameters":["service"],"bindings":{"service":"id"}},"admin.services.update-order":{"uri":"admin\/services\/update-order","methods":["POST"]},"admin.projects.index":{"uri":"admin\/projects","methods":["GET","HEAD"]},"admin.projects.create":{"uri":"admin\/projects\/create","methods":["GET","HEAD"]},"admin.projects.store":{"uri":"admin\/projects","methods":["POST"]},"admin.projects.edit":{"uri":"admin\/projects\/{project}\/edit","methods":["GET","HEAD"],"parameters":["project"]},"admin.projects.update":{"uri":"admin\/projects\/{project}","methods":["PUT"],"parameters":["project"],"bindings":{"project":"id"}},"admin.projects.destroy":{"uri":"admin\/projects\/{project}","methods":["DELETE"],"parameters":["project"],"bindings":{"project":"id"}},"admin.projects.toggle-featured":{"uri":"admin\/projects\/{project}\/toggle-featured","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"admin.skills.index":{"uri":"admin\/skills","methods":["GET","HEAD"]},"admin.skills.store":{"uri":"admin\/skills","methods":["POST"]},"admin.skills.update":{"uri":"admin\/skills\/{skill}","methods":["PUT"],"parameters":["skill"],"bindings":{"skill":"id"}},"admin.skills.destroy":{"uri":"admin\/skills\/{skill}","methods":["DELETE"],"parameters":["skill"],"bindings":{"skill":"id"}},"admin.skills.reorder":{"uri":"admin\/skills\/reorder","methods":["POST"]},"admin.skills.toggle-visibility":{"uri":"admin\/skills\/{skill}\/toggle-visibility","methods":["POST"],"parameters":["skill"],"bindings":{"skill":"id"}},"admin.experiences.index":{"uri":"admin\/experiences","methods":["GET","HEAD"]},"admin.experiences.store":{"uri":"admin\/experiences","methods":["POST"]},"admin.experiences.update":{"uri":"admin\/experiences\/{experience}","methods":["PUT"],"parameters":["experience"],"bindings":{"experience":"id"}},"admin.experiences.destroy":{"uri":"admin\/experiences\/{experience}","methods":["DELETE"],"parameters":["experience"],"bindings":{"experience":"id"}},"admin.experiences.reorder":{"uri":"admin\/experiences\/reorder","methods":["POST"]},"admin.resume":{"uri":"admin\/resume","methods":["GET","HEAD"]},"admin.testimonials.index":{"uri":"admin\/testimonials","methods":["GET","HEAD"]},"admin.testimonials.store":{"uri":"admin\/testimonials","methods":["POST"]},"admin.testimonials.update":{"uri":"admin\/testimonials\/{testimonial}","methods":["PUT"],"parameters":["testimonial"],"bindings":{"testimonial":"id"}},"admin.testimonials.destroy":{"uri":"admin\/testimonials\/{testimonial}","methods":["DELETE"],"parameters":["testimonial"],"bindings":{"testimonial":"id"}},"admin.testimonials.reorder":{"uri":"admin\/testimonials\/reorder","methods":["POST"]},"admin.testimonials.toggle-featured":{"uri":"admin\/testimonials\/{testimonial}\/toggle-featured","methods":["POST"],"parameters":["testimonial"],"bindings":{"testimonial":"id"}},"admin.messages.index":{"uri":"admin\/messages","methods":["GET","HEAD"]},"admin.messages.show":{"uri":"admin\/messages\/{message}","methods":["GET","HEAD"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.read":{"uri":"admin\/messages\/{message}\/read","methods":["POST"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.unread":{"uri":"admin\/messages\/{message}\/unread","methods":["POST"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.reply":{"uri":"admin\/messages\/{message}\/reply","methods":["POST"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.archive":{"uri":"admin\/messages\/{message}\/archive","methods":["POST"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.unarchive":{"uri":"admin\/messages\/{message}\/unarchive","methods":["POST"],"parameters":["message"],"bindings":{"message":"id"}},"admin.messages.destroy":{"uri":"admin\/messages\/{message}","methods":["DELETE"],"parameters":["message"],"bindings":{"message":"id"}},"admin.appearance":{"uri":"admin\/appearance","methods":["GET","HEAD"]},"admin.settings.index":{"uri":"admin\/settings","methods":["GET","HEAD"]},"admin.settings.update":{"uri":"admin\/settings","methods":["POST"]},"admin.settings.clear-cache":{"uri":"admin\/settings\/clear-cache","methods":["POST"]},"admin.settings.export-data":{"uri":"admin\/settings\/export-data","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
