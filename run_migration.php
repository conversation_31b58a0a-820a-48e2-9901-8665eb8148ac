<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// Database configuration
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'dynamic-portfolio',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Check if columns already exist
    $schema = Capsule::schema();
    
    if (!$schema->hasColumn('hero_section', 'facebook_url')) {
        $schema->table('hero_section', function (Blueprint $table) {
            // Facebook
            $table->string('facebook_url')->nullable()->after('twitter_enabled');
            $table->boolean('facebook_enabled')->default(true)->after('facebook_url');
            
            // Instagram
            $table->string('instagram_url')->nullable()->after('facebook_enabled');
            $table->boolean('instagram_enabled')->default(true)->after('instagram_url');
            
            // Dribbble
            $table->string('dribbble_url')->nullable()->after('instagram_enabled');
            $table->boolean('dribbble_enabled')->default(true)->after('dribbble_url');
            
            // Behance
            $table->string('behance_url')->nullable()->after('dribbble_enabled');
            $table->boolean('behance_enabled')->default(true)->after('behance_url');
            
            // Fiverr
            $table->string('fiverr_url')->nullable()->after('behance_enabled');
            $table->boolean('fiverr_enabled')->default(true)->after('fiverr_url');
            
            // Upwork
            $table->string('upwork_url')->nullable()->after('fiverr_enabled');
            $table->boolean('upwork_enabled')->default(true)->after('upwork_url');
        });
        
        echo "Migration completed successfully! Added new social media platform fields.\n";
    } else {
        echo "Migration already applied. Social media fields already exist.\n";
    }
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
}
