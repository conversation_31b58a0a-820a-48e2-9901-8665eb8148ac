-- Manual sync of categories from projects table to projects_management table
-- Run this in phpMyAdmin to force sync categories

-- Step 1: Check current state
SELECT 'Current projects table categories:' as info;
SELECT DISTINCT category FROM projects WHERE category IS NOT NULL AND category != '' ORDER BY category;

SELECT 'Current projects_management filter_categories:' as info;
SELECT id, filter_categories FROM projects_management;

-- Step 2: Get unique categories from projects table and update projects_management
-- You need to manually build the JSON array based on your actual project categories

-- Example: If your projects table has categories: Web Design, E-commerce, Mobile App
-- Update the JSON array below with your actual categories:

UPDATE projects_management 
SET filter_categories = JSON_ARRAY(
    'Web Design',
    'E-commerce', 
    'Mobile App',
    'UI/UX Design',
    'App Development'
)
WHERE id = 1;

-- Step 3: Verify the update
SELECT 'After sync - projects_management filter_categories:' as info;
SELECT id, filter_categories FROM projects_management;

-- Alternative: Clear all categories to force dynamic loading
-- Uncomment the line below if you want to clear all categories:
-- UPDATE projects_management SET filter_categories = JSON_ARRAY() WHERE id = 1;

-- Note: Replace the categories in the JSON_ARRAY() with your actual project categories
-- You can see them in the first query result above
