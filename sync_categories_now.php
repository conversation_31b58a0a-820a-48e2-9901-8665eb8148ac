<?php

// Simple script to sync project categories to projects_management table

require_once 'vendor/autoload.php';

// Load <PERSON> app
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Project;
use App\Models\ProjectsManagement;

echo "🔄 Syncing project categories to projects_management table...\n\n";

try {
    // Get all unique categories from projects table (including drafts for admin)
    $projectCategories = Project::getAllUniqueCategories();
    echo "📋 Found categories in projects table: " . (empty($projectCategories) ? '(none)' : implode(', ', $projectCategories)) . "\n";

    // Get or create projects_management record
    $contentManagement = ProjectsManagement::first();
    if (!$contentManagement) {
        $contentManagement = ProjectsManagement::getOrCreate();
        echo "✨ Created new projects_management record.\n";
    } else {
        echo "📄 Using existing projects_management record (ID: {$contentManagement->id}).\n";
    }

    // Show current filter_categories
    $currentCategories = $contentManagement->filter_categories;
    echo "🔍 Current filter_categories: " . (empty($currentCategories) ? '(empty)' : json_encode($currentCategories)) . "\n";

    // Update filter_categories with current project categories
    $contentManagement->update([
        'filter_categories' => $projectCategories
    ]);

    echo "✅ Successfully synced categories!\n";
    echo "🎯 New filter_categories: " . (empty($projectCategories) ? '(empty)' : json_encode($projectCategories)) . "\n\n";

    echo "🚀 Next steps:\n";
    echo "1. Visit /projects page - should show categories from actual projects\n";
    echo "2. Add new project from admin panel - category should auto-sync\n";
    echo "3. If no categories exist, Featured Work section will be hidden\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📝 Stack trace:\n" . $e->getTraceAsString() . "\n";
}
