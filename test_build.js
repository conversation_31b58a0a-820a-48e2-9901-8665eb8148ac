// Simple test to check if Node.js and npm are working

console.log('Node.js version:', process.version);
console.log('Current directory:', process.cwd());

// Test if we can read package.json
const fs = require('fs');
const path = require('path');

try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    console.log('Package name:', packageJson.name);
    console.log('Available scripts:', Object.keys(packageJson.scripts || {}));
} catch (error) {
    console.error('Error reading package.json:', error.message);
}

console.log('Test completed successfully!');
