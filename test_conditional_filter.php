<?php

// Test the new conditional filter logic

echo "🎯 Testing Conditional Home Page Filter Logic\n\n";

// Test Case 1: No projects
$noProjects = [];
echo "📋 Test Case 1: No projects in database\n";
echo "Projects: " . count($noProjects) . "\n";

$projectCategories1 = count($noProjects) > 0
    ? array_unique(array_column($noProjects, 'category'))
    : [];
$projectCategories1 = array_filter($projectCategories1);

echo "Categories extracted: " . count($projectCategories1) . "\n";
echo "Show filter section: " . (count($projectCategories1) > 0 ? 'YES' : 'NO') . "\n";
echo "Expected: NO filter section should show\n\n";

// Test Case 2: Projects with categories
$projectsWithCategories = [
    ['id' => 1, 'title' => 'Project 1', 'category' => 'Web Design'],
    ['id' => 2, 'title' => 'Project 2', 'category' => 'Mobile App'],
    ['id' => 3, 'title' => 'Project 3', 'category' => 'Web Design'],
];
echo "📋 Test Case 2: Projects with categories\n";
echo "Projects: " . count($projectsWithCategories) . "\n";
foreach ($projectsWithCategories as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

$projectCategories2 = count($projectsWithCategories) > 0
    ? array_unique(array_column($projectsWithCategories, 'category'))
    : [];
$projectCategories2 = array_filter($projectCategories2);

echo "Categories extracted: " . count($projectCategories2) . " (" . implode(', ', $projectCategories2) . ")\n";
echo "Show filter section: " . (count($projectCategories2) > 0 ? 'YES' : 'NO') . "\n";
echo "Filter buttons: All, " . implode(', ', $projectCategories2) . "\n";
echo "Expected: Filter section should show with 3 buttons\n\n";

// Test Case 3: Projects without categories
$projectsWithoutCategories = [
    ['id' => 1, 'title' => 'Project 1', 'category' => ''],
    ['id' => 2, 'title' => 'Project 2', 'category' => null],
    ['id' => 3, 'title' => 'Project 3', 'category' => ''],
];
echo "📋 Test Case 3: Projects without categories\n";
echo "Projects: " . count($projectsWithoutCategories) . "\n";
foreach ($projectsWithoutCategories as $project) {
    echo "  - {$project['title']}: " . ($project['category'] ?: '(empty)') . "\n";
}

$projectCategories3 = count($projectsWithoutCategories) > 0
    ? array_unique(array_column($projectsWithoutCategories, 'category'))
    : [];
$projectCategories3 = array_filter($projectCategories3); // This removes empty/null values

echo "Categories extracted: " . count($projectCategories3) . "\n";
echo "Show filter section: " . (count($projectCategories3) > 0 ? 'YES' : 'NO') . "\n";
echo "Expected: NO filter section should show (empty categories filtered out)\n\n";

echo "✅ EXPECTED BEHAVIOR:\n";
echo "1. No projects → No filter section\n";
echo "2. Projects with categories → Filter section with All + category buttons\n";
echo "3. Projects without categories → No filter section\n";
echo "4. Mixed projects → Filter section with only non-empty categories\n";
echo "5. 100% dynamic - no hardcoded categories\n";

echo "\n🎉 The filter is now completely conditional and dynamic!\n";
