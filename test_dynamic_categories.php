<?php

// Test the new dynamic category logic

echo "🧪 Testing Dynamic Category Logic\n\n";

// Simulate projects data (what frontend receives)
$projects = [
    ['id' => 1, 'title' => 'Project 1', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 2, 'title' => 'Project 2', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 3, 'title' => 'Project 3', 'category' => 'E-commerce', 'status' => 'published'],
    ['id' => 4, 'title' => 'Project 4', 'category' => 'Mobile App', 'status' => 'draft'], // Draft - should not affect frontend
];

echo "📋 Projects in database:\n";
foreach ($projects as $project) {
    echo "  - {$project['title']}: {$project['category']} ({$project['status']})\n";
}

// Simulate what frontend will do (only published projects are passed to frontend)
$publishedProjects = array_filter($projects, function($project) {
    return $project['status'] === 'published';
});

echo "\n📋 Published projects passed to frontend:\n";
foreach ($publishedProjects as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

// Extract unique categories from published projects (frontend logic)
$projectCategories = array_unique(array_column($publishedProjects, 'category'));
$projectCategories = array_filter($projectCategories); // Remove empty values
sort($projectCategories);

echo "\n🎯 Categories that will show in filter:\n";
echo "  - All Categories (always shown)\n";
foreach ($projectCategories as $category) {
    echo "  - {$category}\n";
}

echo "\n✅ Expected behavior:\n";
echo "1. Only 'Web Design' and 'E-commerce' will show (from published projects)\n";
echo "2. 'Mobile App' will NOT show (because it's only in draft projects)\n";
echo "3. Empty categories will NOT show\n";
echo "4. Categories without projects will NOT show\n";
echo "5. Filter section will be visible (because categories exist)\n";

$shouldShowFilter = count($projectCategories) > 0;
echo "\n👁️ Show filter section: " . ($shouldShowFilter ? 'YES' : 'NO') . "\n";
echo "📊 Total filter buttons: " . (count($projectCategories) + 1) . " (including 'All Categories')\n";
