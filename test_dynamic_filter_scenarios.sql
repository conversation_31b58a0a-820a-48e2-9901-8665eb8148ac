-- Test different scenarios for the new dynamic filter

-- Scenario 1: Check current state
SELECT 'Current projects and categories:' as info;
SELECT id, title, category, status 
FROM projects 
WHERE status = 'published'
ORDER BY category, title;

-- Scenario 2: Test with no published projects (filter should disappear)
-- Uncomment to test:
-- UPDATE projects SET status = 'draft';
-- Expected: No filter section on home page

-- Scenario 3: Test with only one category (filter should show All + 1 category)
-- Uncomment to test:
-- UPDATE projects SET status = 'published' WHERE category = 'Web Design';
-- UPDATE projects SET status = 'draft' WHERE category != 'Web Design';
-- Expected: Filter shows "All" and "Web Design" only

-- Scenario 4: Test with multiple categories (filter should show All + all categories)
-- Uncomment to test:
-- UPDATE projects SET status = 'published' WHERE category IN ('Web Design', 'Mobile App', 'E-commerce');
-- Expected: Filter shows "All", "E-commerce", "Mobile App", "Web Design" (sorted)

-- Scenario 5: Test with projects that have empty categories (should be filtered out)
-- Uncomment to test:
-- UPDATE projects SET category = '' WHERE id = 1;
-- UPDATE projects SET category = NULL WHERE id = 2;
-- Expected: Empty categories don't appear in filter

-- Scenario 6: Restore to normal state
-- UPDATE projects SET status = 'published' WHERE id <= 6;
-- UPDATE projects SET category = 'Web Design' WHERE id IN (1, 2);
-- UPDATE projects SET category = 'Mobile App' WHERE id IN (3, 4);
-- UPDATE projects SET category = 'E-commerce' WHERE id IN (5, 6);

-- Check final state
SELECT 'Final state - published projects:' as info;
SELECT DISTINCT category, COUNT(*) as project_count
FROM projects 
WHERE status = 'published' 
  AND category IS NOT NULL 
  AND category != ''
GROUP BY category 
ORDER BY category;

-- Note: After any changes, refresh the home page to see the dynamic filter update
