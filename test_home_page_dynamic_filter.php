<?php

// Test the new home page dynamic filter logic

echo "🏠 Testing New Home Page Dynamic Filter Logic\n\n";

// Simulate ALL published projects (what WelcomeController now passes)
$allProjects = [
    ['id' => 1, 'title' => 'Project 1', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 2, 'title' => 'Project 2', 'category' => 'E-commerce', 'status' => 'published'],
    ['id' => 3, 'title' => 'Project 3', 'category' => 'Mobile App', 'status' => 'published'],
    ['id' => 4, 'title' => 'Project 4', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 5, 'title' => 'Project 5', 'category' => 'App Development', 'status' => 'published'],
    ['id' => 6, 'title' => 'Project 6', 'category' => 'UI/UX Design', 'status' => 'published'],
    ['id' => 7, 'title' => 'Project 7', 'category' => 'Web Development', 'status' => 'published'],
    ['id' => 8, 'title' => 'Project 8', 'category' => 'Web Design', 'status' => 'published'],
];

echo "📋 ALL published projects passed to home page:\n";
foreach ($allProjects as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

// Extract categories from ALL projects (line 809 logic)
$allCategories = array_unique(array_column($allProjects, 'category'));
$allCategories = array_filter($allCategories);
sort($allCategories);

echo "\n🎯 Filter buttons that will show (from ALL projects):\n";
echo "  - All\n";
foreach ($allCategories as $category) {
    echo "  - {$category}\n";
}

// Test filtering logic
echo "\n📊 Filtering behavior:\n";

// When "All" is selected - show first 6
$filteredForAll = array_slice($allProjects, 0, 6);
echo "\n🔹 When 'All' is selected (first 6 projects):\n";
foreach ($filteredForAll as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

// When "Web Design" is selected - show all matching
$filteredForWebDesign = array_filter($allProjects, function($project) {
    return $project['category'] === 'Web Design';
});
echo "\n🔹 When 'Web Design' is selected (all matching projects):\n";
foreach ($filteredForWebDesign as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

// When "Mobile App" is selected - show all matching
$filteredForMobileApp = array_filter($allProjects, function($project) {
    return $project['category'] === 'Mobile App';
});
echo "\n🔹 When 'Mobile App' is selected (all matching projects):\n";
foreach ($filteredForMobileApp as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

echo "\n✅ EXPECTED RESULTS:\n";
echo "1. Filter shows ALL available categories (like projects page)\n";
echo "2. Default view shows first 6 projects\n";
echo "3. Category filtering shows all matching projects\n";
echo "4. Home page filter now matches projects page filter exactly\n";

echo "\n🎉 SUCCESS: Home page filter is now fully dynamic like projects page!\n";
