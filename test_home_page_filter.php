<?php

// Test what categories show on home page filter

echo "🏠 Testing Home Page Filter Logic\n\n";

// Simulate what WelcomeController does - get first 6 published projects
$allProjects = [
    ['id' => 1, 'title' => 'Project 1', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 2, 'title' => 'Project 2', 'category' => 'Mobile App', 'status' => 'published'],
    ['id' => 3, 'title' => 'Project 3', 'category' => 'UI/UX Design', 'status' => 'published'],
    ['id' => 4, 'title' => 'Project 4', 'category' => 'Web Development', 'status' => 'published'],
    ['id' => 5, 'title' => 'Project 5', 'category' => 'E-commerce', 'status' => 'published'],
    ['id' => 6, 'title' => 'Project 6', 'category' => 'Web Design', 'status' => 'published'],
    ['id' => 7, 'title' => 'Project 7', 'category' => 'Branding', 'status' => 'published'],
    ['id' => 8, 'title' => 'Project 8', 'category' => 'Mobile App', 'status' => 'draft'], // Won't show on home
];

// Filter published projects and take first 6 (like WelcomeController)
$publishedProjects = array_filter($allProjects, function($project) {
    return $project['status'] === 'published';
});
$homePageProjects = array_slice($publishedProjects, 0, 6);

echo "📋 Projects shown on home page (first 6 published):\n";
foreach ($homePageProjects as $project) {
    echo "  - {$project['title']}: {$project['category']}\n";
}

// Extract categories like frontend does: [...new Set(projects.map(project => project.category).filter(Boolean))].sort()
$projectCategories = array_unique(array_column($homePageProjects, 'category'));
$projectCategories = array_filter($projectCategories); // Remove empty values
sort($projectCategories);

echo "\n🎯 Filter buttons that will show on home page:\n";
echo "  - All (always shown)\n";
foreach ($projectCategories as $category) {
    echo "  - {$category}\n";
}

echo "\n❗ ISSUE: If you see 'Mobile App', 'UI/UX Design', 'Web Development' on your home page,\n";
echo "   it means you have published projects with these categories in your database.\n";

echo "\n✅ SOLUTION: The home page filter is already dynamic!\n";
echo "   You just need to clean up your database projects:\n";
echo "   1. Check what published projects you have\n";
echo "   2. Update or delete projects with unwanted categories\n";
echo "   3. The home page filter will automatically update\n";

echo "\n📊 Current home page logic (Line 809 in welcome.tsx):\n";
echo "   [...new Set(projects.map(project => project.category).filter(Boolean))].sort()\n";
echo "   This extracts categories from actual projects passed to the page.\n";
