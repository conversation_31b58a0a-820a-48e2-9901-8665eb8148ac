<?php

namespace Tests\Feature;

use App\Models\HeroSection;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AvailabilityToggleTest extends TestCase
{
    use RefreshDatabase;

    public function test_availability_toggle_updates_hero_section()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create initial hero section with availability disabled
        $heroSection = HeroSection::factory()->create([
            'is_available' => false,
        ]);

        // Test enabling availability
        $response = $this->post('/admin/profile/hero', [
            'fullName' => 'Test User',
            'title' => 'Test Title',
            'about' => 'Test about section',
            'yearsExperience' => 5,
            'projectsCompleted' => 10,
            'isAvailable' => true,
            'ctaText' => 'Hire Me',
            'ctaSecondaryText' => 'Download CV',
            'ctaUrl' => '#contact',
            'ctaSecondaryUrl' => '#portfolio',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'location' => 'Test City',
        ]);

        $response->assertRedirect();
        
        $heroSection->refresh();
        $this->assertTrue($heroSection->is_available);

        // Test disabling availability
        $response = $this->post('/admin/profile/hero', [
            'fullName' => 'Test User',
            'title' => 'Test Title',
            'about' => 'Test about section',
            'yearsExperience' => 5,
            'projectsCompleted' => 10,
            'isAvailable' => false,
            'ctaText' => 'Hire Me',
            'ctaSecondaryText' => 'Download CV',
            'ctaUrl' => '#contact',
            'ctaSecondaryUrl' => '#portfolio',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'location' => 'Test City',
        ]);

        $response->assertRedirect();
        
        $heroSection->refresh();
        $this->assertFalse($heroSection->is_available);
    }

    public function test_homepage_reflects_availability_status()
    {
        // Test with availability enabled
        $heroSection = HeroSection::factory()->create([
            'is_available' => true,
        ]);

        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->where('profile.is_available', true)
        );

        // Test with availability disabled - this is the critical test
        $heroSection->update(['is_available' => false]);

        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->where('profile.is_available', false)
        );
    }

    public function test_homepage_correctly_handles_false_availability()
    {
        // Specifically test that false is not treated as null/undefined
        $heroSection = HeroSection::factory()->create([
            'is_available' => false,
        ]);

        $response = $this->get('/');
        $response->assertStatus(200);

        // Verify that the profile data contains exactly false, not true
        $response->assertInertia(fn ($page) =>
            $page->where('profile.is_available', false)
        );
    }

    public function test_admin_profile_shows_current_availability_status()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'is_available' => true,
        ]);

        $response = $this->get('/admin/profile');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->where('profile.isAvailable', true)
        );
    }
}
