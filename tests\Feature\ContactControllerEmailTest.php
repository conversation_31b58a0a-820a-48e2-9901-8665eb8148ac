<?php

namespace Tests\Feature;

use App\Models\ContactMessage;
use App\Models\SmtpConfiguration;
use App\Mail\ContactNotification;
use App\Mail\ContactAutoReply;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class ContactControllerEmailTest extends TestCase
{
    use RefreshDatabase;

    public function test_contact_form_stores_message_and_sends_emails()
    {
        Mail::fake();

        // Setup SMTP configuration
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
        ];

        $response = $this->post(route('contact.store'), $contactData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify message was stored
        $this->assertDatabaseHas('contact_messages', $contactData);

        // Verify emails were sent (2 emails: admin notification + auto-reply)
        Mail::assertSent(ContactNotification::class, 1);
        Mail::assertSent(ContactAutoReply::class, 1);
    }

    public function test_contact_form_works_even_when_email_fails()
    {
        // Setup SMTP configuration with invalid settings to cause email failure
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
            'host' => 'invalid-host',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
        ];

        $response = $this->post(route('contact.store'), $contactData);

        // Contact form should still work even if email fails
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify message was still stored
        $this->assertDatabaseHas('contact_messages', $contactData);
    }

    public function test_contact_form_validation()
    {
        $response = $this->postJson(route('contact.store'), [
            'name' => '', // Required
            'email' => 'invalid-email', // Must be valid email
            'subject' => '', // Required
            'message' => '', // Required
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name', 'email', 'subject', 'message']);
    }

    public function test_contact_form_does_not_send_emails_when_smtp_disabled()
    {
        Mail::fake();

        // Setup SMTP configuration as disabled
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update(['enabled' => false]);

        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
        ];

        $response = $this->post(route('contact.store'), $contactData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify message was stored
        $this->assertDatabaseHas('contact_messages', $contactData);

        // Verify no emails were sent
        Mail::assertNothingSent();
    }

    public function test_contact_form_sends_only_auto_reply_when_admin_notifications_disabled()
    {
        Mail::fake();

        // Setup SMTP configuration with admin notifications disabled
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => false, // Disabled
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
        ];

        $response = $this->post(route('contact.store'), $contactData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify message was stored
        $this->assertDatabaseHas('contact_messages', $contactData);

        // Verify only auto-reply was sent (1 email)
        Mail::assertSent(ContactAutoReply::class, 1);
        Mail::assertNotSent(ContactNotification::class);
    }

    public function test_contact_message_creation_updates_status_correctly()
    {
        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
        ];

        $response = $this->post(route('contact.store'), $contactData);

        $response->assertStatus(200);

        $contactMessage = ContactMessage::where('email', '<EMAIL>')->first();
        $this->assertNotNull($contactMessage);
        $this->assertEquals('unread', $contactMessage->status);
        $this->assertNull($contactMessage->read_at);
        $this->assertNull($contactMessage->replied_at);
        $this->assertNull($contactMessage->archived_at);
    }
}
