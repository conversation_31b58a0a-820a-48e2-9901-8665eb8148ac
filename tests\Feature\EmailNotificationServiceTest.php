<?php

namespace Tests\Feature;

use App\Models\ContactMessage;
use App\Models\SmtpConfiguration;
use App\Services\EmailNotificationService;
use App\Mail\ContactNotification;
use App\Mail\ContactAutoReply;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class EmailNotificationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected EmailNotificationService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailService = new EmailNotificationService();
    }

    public function test_sends_contact_notification_when_enabled()
    {
        Mail::fake();

        // Setup SMTP configuration
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        // Create a contact message
        $contactMessage = ContactMessage::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
        ]);

        $result = $this->emailService->sendContactNotification($contactMessage);

        $this->assertTrue($result);
        Mail::assertSent(ContactNotification::class);
    }

    public function test_does_not_send_notification_when_disabled()
    {
        Mail::fake();

        // Setup SMTP configuration as disabled
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => false,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
        ]);

        $contactMessage = ContactMessage::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
        ]);

        $result = $this->emailService->sendContactNotification($contactMessage);

        $this->assertFalse($result);
        Mail::assertNothingSent();
    }

    public function test_does_not_send_notification_when_admin_notifications_disabled()
    {
        Mail::fake();

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'admin_email' => '<EMAIL>',
            'admin_notifications' => false, // Disabled
        ]);

        $contactMessage = ContactMessage::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
        ]);

        $result = $this->emailService->sendContactNotification($contactMessage);

        $this->assertFalse($result);
        Mail::assertNothingSent();
    }

    public function test_sends_auto_reply_when_enabled()
    {
        Mail::fake();

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $contactMessage = ContactMessage::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
        ]);

        $result = $this->emailService->sendContactAutoReply($contactMessage);

        $this->assertTrue($result);
        Mail::assertSent(ContactAutoReply::class);
    }

    public function test_does_not_send_auto_reply_when_disabled()
    {
        Mail::fake();

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update(['enabled' => false]);

        $contactMessage = ContactMessage::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
        ]);

        $result = $this->emailService->sendContactAutoReply($contactMessage);

        $this->assertFalse($result);
        Mail::assertNothingSent();
    }

    public function test_can_send_test_email()
    {
        Mail::fake();

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
            'test_email_subject' => 'Test Subject',
            'test_email_body' => 'Test Body',
        ]);

        $result = $this->emailService->sendTestEmail('<EMAIL>');

        $this->assertTrue($result);
        // For test emails using Mail::raw, we just verify the method succeeded
        // The actual email sending is tested in integration tests
    }

    public function test_test_email_throws_exception_when_disabled()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update(['enabled' => false]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('SMTP is not enabled');

        $this->emailService->sendTestEmail('<EMAIL>');
    }

    public function test_is_enabled_returns_correct_status()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        
        $smtpConfig->update(['enabled' => true]);
        $this->assertTrue($this->emailService->isEnabled());

        $smtpConfig->update(['enabled' => false]);
        $this->assertFalse($this->emailService->isEnabled());
    }

    public function test_is_admin_notification_enabled_returns_correct_status()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        
        $smtpConfig->update([
            'enabled' => true,
            'admin_notifications' => true,
            'admin_email' => '<EMAIL>',
        ]);
        $this->assertTrue($this->emailService->isAdminNotificationEnabled());

        $smtpConfig->update(['admin_notifications' => false]);
        $this->assertFalse($this->emailService->isAdminNotificationEnabled());

        $smtpConfig->update(['enabled' => false, 'admin_notifications' => true]);
        $this->assertFalse($this->emailService->isAdminNotificationEnabled());

        $smtpConfig->update(['enabled' => true, 'admin_notifications' => true, 'admin_email' => null]);
        $this->assertFalse($this->emailService->isAdminNotificationEnabled());
    }
}
