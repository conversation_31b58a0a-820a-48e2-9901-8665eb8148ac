<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Service;
use App\Models\ServicesManagement;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ServicesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user for authenticated tests
        $this->admin = \App\Models\User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    public function test_services_management_returns_default_data_when_database_is_empty()
    {
        // Clear the database first
        \DB::table('services_management')->truncate();

        // Ensure database is empty
        $this->assertDatabaseCount('services_management', 0);
        
        // Call getOrCreate method
        $servicesManagement = ServicesManagement::getOrCreate();
        
        // Verify default data is created
        $this->assertNotNull($servicesManagement);
        $this->assertEquals('Professional Services', $servicesManagement->services_section_badge);
        $this->assertEquals('Areas of Expertise', $servicesManagement->services_section_title);
        $this->assertEquals('Delivering tailored, high-quality solutions to help your business thrive in the digital landscape', $servicesManagement->services_section_description);
        $this->assertEquals('Explore All Services', $servicesManagement->services_button_text);
        $this->assertEquals('Professional Services', $servicesManagement->services_page_title);
        $this->assertEquals('Comprehensive digital solutions tailored to your business needs.', $servicesManagement->services_page_description);
        
        // Verify benefits
        $this->assertEquals('Fast Delivery', $servicesManagement->services_benefit_1_text);
        $this->assertEquals('Zap', $servicesManagement->services_benefit_1_icon);
        $this->assertEquals('Quality Guaranteed', $servicesManagement->services_benefit_2_text);
        $this->assertEquals('CheckCircle', $servicesManagement->services_benefit_2_icon);
        $this->assertEquals('24/7 Support', $servicesManagement->services_benefit_3_text);
        $this->assertEquals('Clock', $servicesManagement->services_benefit_3_icon);
        
        // Verify work process
        $this->assertEquals('My Work Process', $servicesManagement->work_process_title);
        $this->assertEquals('A systematic approach that ensures quality results and client satisfaction.', $servicesManagement->work_process_description);
        
        // Verify CTA
        $this->assertEquals('Ready to Start Your Project?', $servicesManagement->services_cta_title);
        $this->assertEquals('Let us discuss your requirements and create something amazing together.', $servicesManagement->services_cta_description);
        $this->assertEquals('Get Free Consultation', $servicesManagement->services_cta_primary_text);
        $this->assertEquals('View Portfolio', $servicesManagement->services_cta_secondary_text);
        
        // Verify record was saved to database
        $this->assertDatabaseCount('services_management', 1);
    }

    public function test_public_services_page_loads_with_default_content()
    {
        $response = $this->get('/services');
        
        $response->assertOk();
        
        // Verify that default content is passed to the view
        $response->assertInertia(fn ($page) => 
            $page->component('services')
                ->has('content')
                ->where('content.page_title', 'Professional Services')
                ->where('content.page_description', 'Comprehensive digital solutions tailored to your business needs.')
                ->has('content.benefits')
                ->has('content.work_process')
                ->has('content.cta')
        );
    }

    public function test_home_page_loads_with_default_services_content()
    {
        $response = $this->get('/');

        $response->assertOk();

        // Verify that default services content is passed to the home page
        $response->assertInertia(fn ($page) =>
            $page->component('welcome')
                ->has('servicesContent')
                ->where('servicesContent.badge', 'Professional Services')
                ->where('servicesContent.title', 'Areas of Expertise')
                ->where('servicesContent.description', 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape')
                ->where('servicesContent.button_text', 'Explore All Services')
        );
    }

    public function test_admin_services_page_loads_successfully()
    {
        $response = $this->actingAs($this->admin)->get('/admin/services');
        
        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('admin/services')
                ->has('services')
                ->has('content')
        );
    }

    public function test_service_can_be_created_with_valid_data()
    {
        Storage::fake('public');
        
        $serviceData = [
            'title' => 'Test Service',
            'description' => 'Test service description',
            'long_description' => 'Detailed test service description',
            'icon' => 'Code',
            'price' => 1500.00,
            'duration' => '2-3 weeks',
            'projects_count' => 10,
            'is_active' => true,
            'is_featured' => false,
            'features' => json_encode(['Feature 1', 'Feature 2', 'Feature 3']),
            'technologies' => json_encode(['React', 'Laravel', 'MySQL']),
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/services', $serviceData);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('services', [
            'title' => 'Test Service',
            'description' => 'Test service description',
            'icon' => 'Code',
            'price' => 1500.00,
            'is_active' => true,
        ]);
    }

    public function test_service_can_be_updated()
    {
        $service = Service::factory()->create([
            'title' => 'Original Title',
            'description' => 'Original description',
            'price' => 1000.00,
        ]);

        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'long_description' => 'Updated long description',
            'icon' => 'Palette',
            'price' => 2000.00,
            'duration' => '3-4 weeks',
            'projects_count' => 15,
            'is_active' => true,
            'is_featured' => true,
            'features' => json_encode(['Updated Feature 1', 'Updated Feature 2']),
            'technologies' => json_encode(['Vue.js', 'Node.js']),
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/services/{$service->id}", $updateData);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('services', [
            'id' => $service->id,
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'price' => 2000.00,
            'is_featured' => true,
        ]);
    }

    public function test_service_can_be_deleted()
    {
        $service = Service::factory()->create();

        $response = $this->actingAs($this->admin)
            ->delete("/admin/services/{$service->id}");

        $response->assertRedirect();
        $this->assertDatabaseMissing('services', ['id' => $service->id]);
    }

    public function test_services_content_can_be_updated()
    {
        // Clear existing data first
        \DB::table('services_management')->truncate();

        $contentData = [
            'services_section_badge' => 'Updated Badge',
            'services_section_title' => 'Updated Title',
            'services_section_description' => 'Updated description',
            'services_button_text' => 'Updated Button',
            'services_page_title' => 'Updated Page Title',
            'services_page_description' => 'Updated page description',
            'services_benefit_1_text' => 'Updated Benefit 1',
            'services_benefit_1_icon' => 'Star',
            'services_benefit_2_text' => 'Updated Benefit 2',
            'services_benefit_2_icon' => 'Shield',
            'services_benefit_3_text' => 'Updated Benefit 3',
            'services_benefit_3_icon' => 'Award',
            'work_process_title' => 'Updated Process Title',
            'work_process_description' => 'Updated process description',
            'work_process_steps' => [
                ['number' => '01', 'title' => 'Step 1', 'description' => 'Step 1 description'],
                ['number' => '02', 'title' => 'Step 2', 'description' => 'Step 2 description'],
            ],
            'services_cta_title' => 'Updated CTA Title',
            'services_cta_description' => 'Updated CTA description',
            'services_cta_primary_text' => 'Updated Primary Button',
            'services_cta_secondary_text' => 'Updated Secondary Button',
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/services/content', $contentData);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('services_management', [
            'services_section_badge' => 'Updated Badge',
            'services_section_title' => 'Updated Title',
            'services_page_title' => 'Updated Page Title',
            'work_process_title' => 'Updated Process Title',
            'services_cta_title' => 'Updated CTA Title',
        ]);
    }

    public function test_home_page_shows_maximum_6_services()
    {
        // Create 10 services, 4 featured and 6 non-featured
        Service::factory()->count(4)->create(['is_featured' => true, 'is_active' => true]);
        Service::factory()->count(6)->create(['is_featured' => false, 'is_active' => true]);

        $response = $this->get('/');

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->component('welcome')
                ->has('services', 6) // Should show exactly 6 services
        );
    }

    public function test_home_page_fills_with_non_featured_services_when_less_than_6_featured()
    {
        // Create 2 featured and 8 non-featured services
        Service::factory()->count(2)->create(['is_featured' => true, 'is_active' => true]);
        Service::factory()->count(8)->create(['is_featured' => false, 'is_active' => true]);

        $response = $this->get('/');

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->component('welcome')
                ->has('services', 6) // Should show 2 featured + 4 non-featured = 6 total
        );
    }

    public function test_services_are_ordered_correctly()
    {
        // Create services with specific order
        $service1 = Service::factory()->create(['order' => 3, 'is_active' => true]);
        $service2 = Service::factory()->create(['order' => 1, 'is_active' => true]);
        $service3 = Service::factory()->create(['order' => 2, 'is_active' => true]);

        $response = $this->get('/services');

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->component('services')
                ->has('services', 3)
                ->where('services.0.id', $service2->id) // order 1
                ->where('services.1.id', $service3->id) // order 2
                ->where('services.2.id', $service1->id) // order 3
        );
    }

    public function test_only_active_services_shown_on_public_pages()
    {
        Service::factory()->count(3)->create(['is_active' => true]);
        Service::factory()->count(2)->create(['is_active' => false]);

        $response = $this->get('/services');

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->component('services')
                ->has('services', 3) // Only active services
        );
    }
}
