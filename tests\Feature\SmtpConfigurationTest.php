<?php

namespace Tests\Feature;

use App\Models\SmtpConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class SmtpConfigurationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    public function test_can_view_smtp_configuration_page()
    {
        $response = $this->get(route('admin.smtp-config'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/smtp-config')
                 ->has('smtpConfig')
                 ->has('encryptionOptions')
                 ->has('providerPresets')
        );
    }

    public function test_can_update_smtp_configuration()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();

        $updateData = [
            'enabled' => true,
            'mailer' => 'smtp',
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '<EMAIL>',
            'password' => 'test-password',
            'encryption' => 'tls',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
            'admin_email' => '<EMAIL>',
            'admin_notifications' => true,
            'test_email_subject' => 'Test Subject',
            'test_email_body' => 'Test Body',
        ];

        $response = $this->put(route('admin.smtp-config.update'), $updateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $smtpConfig->refresh();
        $this->assertTrue($smtpConfig->enabled);
        $this->assertEquals('smtp.gmail.com', $smtpConfig->host);
        $this->assertEquals(587, $smtpConfig->port);
        $this->assertEquals('<EMAIL>', $smtpConfig->admin_email);
    }

    public function test_smtp_configuration_validation()
    {
        $response = $this->put(route('admin.smtp-config.update'), [
            'enabled' => true,
            'host' => '', // Required when enabled
            'port' => 'invalid', // Must be integer
            'admin_email' => 'invalid-email', // Must be valid email
        ]);

        $response->assertSessionHasErrors(['host', 'port', 'admin_email']);
    }

    public function test_can_send_test_email()
    {
        Mail::fake();

        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '<EMAIL>',
            'password' => 'test-password',
            'encryption' => 'tls',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $response = $this->post(route('admin.smtp-config.test'), [
            'test_email' => '<EMAIL>',
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify test status was updated
        $smtpConfig->refresh();
        $this->assertNotNull($smtpConfig->last_tested_at);
    }

    public function test_test_email_validation()
    {
        $response = $this->post(route('admin.smtp-config.test'), [
            'test_email' => 'invalid-email',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['test_email']);
    }

    public function test_smtp_configuration_model_encryption()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $password = 'test-password';

        $smtpConfig->password = $password;
        $smtpConfig->save();

        // Password should be encrypted in database
        $this->assertNotEquals($password, $smtpConfig->getAttributes()['password']);

        // But should decrypt correctly when accessed
        $this->assertEquals($password, $smtpConfig->password);
    }

    public function test_smtp_configuration_apply_to_mail_config()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update([
            'enabled' => true,
            'host' => 'smtp.test.com',
            'port' => 587,
            'username' => '<EMAIL>',
            'password' => 'test-password',
            'encryption' => 'tls',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test Portfolio',
        ]);

        $result = $smtpConfig->applyToMailConfig();

        $this->assertTrue($result);
        $this->assertEquals('smtp', config('mail.default'));
        $this->assertEquals('smtp.test.com', config('mail.mailers.smtp.host'));
        $this->assertEquals(587, config('mail.mailers.smtp.port'));
    }

    public function test_disabled_smtp_does_not_apply_config()
    {
        $smtpConfig = SmtpConfiguration::getOrCreate();
        $smtpConfig->update(['enabled' => false]);

        $result = $smtpConfig->applyToMailConfig();

        $this->assertFalse($result);
    }

    public function test_get_provider_presets()
    {
        $presets = SmtpConfiguration::getProviderPresets();

        $this->assertIsArray($presets);
        $this->assertArrayHasKey('gmail', $presets);
        $this->assertArrayHasKey('outlook', $presets);
        $this->assertEquals('smtp.gmail.com', $presets['gmail']['host']);
        $this->assertEquals(587, $presets['gmail']['port']);
    }

    public function test_get_encryption_options()
    {
        $options = SmtpConfiguration::getEncryptionOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('tls', $options);
        $this->assertArrayHasKey('ssl', $options);
        $this->assertArrayHasKey('none', $options);
    }
}
