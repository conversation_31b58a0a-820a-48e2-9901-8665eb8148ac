<?php

namespace Tests\Feature;

use App\Models\HeroSection;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SocialMediaAutoDisableTest extends TestCase
{
    use RefreshDatabase;

    public function test_social_media_platforms_can_be_enabled_without_urls()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'github_url' => '',
            'github_enabled' => true,
            'twitter_url' => '',
            'twitter_enabled' => true,
            'linkedin_url' => '',
            'linkedin_enabled' => true,
            'facebook_url' => '',
            'facebook_enabled' => true,
            'instagram_url' => '',
            'instagram_enabled' => true,
            'dribbble_url' => '',
            'dribbble_enabled' => true,
            'behance_url' => '',
            'behance_enabled' => true,
            'upassign_url' => '',
            'upassign_enabled' => true,
            'fiverr_url' => '',
            'fiverr_enabled' => true,
            'upwork_url' => '',
            'upwork_enabled' => true,
            'freelancer_url' => '',
            'freelancer_enabled' => true,
            'peopleperhour_url' => '',
            'peopleperhour_enabled' => true,
        ]);

        // Test that the admin profile page loads correctly
        $response = $this->get('/admin/profile');
        $response->assertStatus(200);

        // Test that switches can be enabled even without URLs (fixed circular dependency)
        $response->assertInertia(fn ($page) =>
            $page->has('profile')
                 ->where('profile.githubUrl', '')
                 ->where('profile.githubEnabled', true)
                 ->where('profile.twitterUrl', '')
                 ->where('profile.twitterEnabled', true)
        );
    }

    public function test_social_media_platforms_enable_when_url_provided()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'github_url' => null,
            'github_enabled' => false,
            'twitter_url' => null,
            'twitter_enabled' => false,
        ]);

        // Submit form with URLs provided
        $response = $this->post('/admin/profile/social', [
            'githubUrl' => 'https://github.com/testuser',
            'githubEnabled' => true,
            'twitterUrl' => 'https://twitter.com/testuser',
            'twitterEnabled' => true,
            'linkedinUrl' => '',
            'linkedinEnabled' => false,
            'facebookUrl' => '',
            'facebookEnabled' => false,
            'instagramUrl' => '',
            'instagramEnabled' => false,
            'dribbbleUrl' => '',
            'dribbbleEnabled' => false,
            'behanceUrl' => '',
            'behanceEnabled' => false,
            'upassignUrl' => '',
            'upassignEnabled' => false,
            'fiverrUrl' => '',
            'fiverrEnabled' => false,
            'upworkUrl' => '',
            'upworkEnabled' => false,
            'freelancerUrl' => '',
            'freelancerEnabled' => false,
            'peopleperhourUrl' => '',
            'peopleperhourEnabled' => false,
        ]);

        $response->assertRedirect();

        $heroSection->refresh();

        // Verify that platforms with URLs are enabled
        $this->assertEquals('https://github.com/testuser', $heroSection->github_url);
        $this->assertTrue($heroSection->github_enabled);
        $this->assertEquals('https://twitter.com/testuser', $heroSection->twitter_url);
        $this->assertTrue($heroSection->twitter_enabled);

        // Verify that platforms without URLs remain disabled
        $this->assertEmpty($heroSection->linkedin_url);
        $this->assertFalse($heroSection->linkedin_enabled);
    }

    public function test_frontend_only_displays_enabled_platforms_with_urls()
    {
        $heroSection = HeroSection::factory()->create([
            'github_url' => 'https://github.com/testuser',
            'github_enabled' => true,
            'twitter_url' => '',
            'twitter_enabled' => false,
            'linkedin_url' => 'https://linkedin.com/in/testuser',
            'linkedin_enabled' => true,
            'facebook_url' => 'https://facebook.com/testuser',
            'facebook_enabled' => false, // Disabled even though URL exists
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);

        // Debug: Let's see what's actually being passed
        $response->assertInertia(fn ($page) =>
            $page->has('profile.social')
        );

        // Get the actual social data to debug
        $socialData = $response->getOriginalContent()->getData()['page']['props']['profile']['social'];

        // Assert specific conditions
        $this->assertNotNull($socialData['github'], 'GitHub should be present (enabled + URL)');
        $this->assertNull($socialData['twitter'], 'Twitter should be missing (no URL)');
        $this->assertNotNull($socialData['linkedin'], 'LinkedIn should be present (enabled + URL)');
        $this->assertNull($socialData['facebook'], 'Facebook should be missing (disabled)');
    }
}
