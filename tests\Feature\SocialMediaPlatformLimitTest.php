<?php

namespace Tests\Feature;

use App\Models\HeroSection;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SocialMediaPlatformLimitTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_enable_up_to_3_social_media_platforms()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'github_enabled' => false,
            'twitter_enabled' => false,
            'linkedin_enabled' => false,
            'facebook_enabled' => false,
            'instagram_enabled' => false,
            'dribbble_enabled' => false,
            'behance_enabled' => false,
            'upassign_enabled' => false,
            'fiverr_enabled' => false,
            'upwork_enabled' => false,
            'freelancer_enabled' => false,
            'peopleperhour_enabled' => false,
        ]);

        // Enable 3 platforms - should work fine
        $response = $this->post('/admin/profile/social', [
            'githubEnabled' => true,
            'githubUrl' => 'https://github.com/testuser',
            'twitterEnabled' => true,
            'twitterUrl' => 'https://twitter.com/testuser',
            'linkedinEnabled' => true,
            'linkedinUrl' => 'https://linkedin.com/in/testuser',
            'facebookEnabled' => false,
            'facebookUrl' => '',
            'instagramEnabled' => false,
            'instagramUrl' => '',
            'dribbbleEnabled' => false,
            'dribbbleUrl' => '',
            'behanceEnabled' => false,
            'behanceUrl' => '',
            'upassignEnabled' => false,
            'upassignUrl' => '',
            'fiverrEnabled' => false,
            'fiverrUrl' => '',
            'upworkEnabled' => false,
            'upworkUrl' => '',
            'freelancerEnabled' => false,
            'freelancerUrl' => '',
            'peopleperhourEnabled' => false,
            'peopleperhourUrl' => '',
        ]);

        $response->assertRedirect();

        $heroSection->refresh();

        // Verify exactly 3 platforms are enabled
        $enabledCount = collect([
            $heroSection->github_enabled,
            $heroSection->twitter_enabled,
            $heroSection->linkedin_enabled,
            $heroSection->facebook_enabled,
            $heroSection->instagram_enabled,
            $heroSection->dribbble_enabled,
            $heroSection->behance_enabled,
            $heroSection->upassign_enabled,
            $heroSection->fiverr_enabled,
            $heroSection->upwork_enabled,
            $heroSection->freelancer_enabled,
            $heroSection->peopleperhour_enabled,
        ])->filter()->count();

        $this->assertEquals(3, $enabledCount);
        $this->assertTrue($heroSection->github_enabled);
        $this->assertTrue($heroSection->twitter_enabled);
        $this->assertTrue($heroSection->linkedin_enabled);
    }

    public function test_backend_accepts_more_than_3_enabled_platforms()
    {
        // Note: The 3-platform limit is enforced on the frontend only
        // The backend should still accept the data as submitted
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'github_enabled' => false,
            'twitter_enabled' => false,
            'linkedin_enabled' => false,
            'facebook_enabled' => false,
        ]);

        // Try to enable 4 platforms via direct backend submission
        $response = $this->post('/admin/profile/social', [
            'githubEnabled' => true,
            'githubUrl' => 'https://github.com/testuser',
            'twitterEnabled' => true,
            'twitterUrl' => 'https://twitter.com/testuser',
            'linkedinEnabled' => true,
            'linkedinUrl' => 'https://linkedin.com/in/testuser',
            'facebookEnabled' => true,
            'facebookUrl' => 'https://facebook.com/testuser',
            'instagramEnabled' => false,
            'instagramUrl' => '',
            'dribbbleEnabled' => false,
            'dribbbleUrl' => '',
            'behanceEnabled' => false,
            'behanceUrl' => '',
            'upassignEnabled' => false,
            'upassignUrl' => '',
            'fiverrEnabled' => false,
            'fiverrUrl' => '',
            'upworkEnabled' => false,
            'upworkUrl' => '',
            'freelancerEnabled' => false,
            'freelancerUrl' => '',
            'peopleperhourEnabled' => false,
            'peopleperhourUrl' => '',
        ]);

        $response->assertRedirect();

        $heroSection->refresh();

        // Backend should accept all 4 enabled platforms
        $this->assertTrue($heroSection->github_enabled);
        $this->assertTrue($heroSection->twitter_enabled);
        $this->assertTrue($heroSection->linkedin_enabled);
        $this->assertTrue($heroSection->facebook_enabled);
    }

    public function test_frontend_only_displays_maximum_3_platforms()
    {
        $heroSection = HeroSection::factory()->create([
            'github_url' => 'https://github.com/testuser',
            'github_enabled' => true,
            'twitter_url' => 'https://twitter.com/testuser',
            'twitter_enabled' => true,
            'linkedin_url' => 'https://linkedin.com/in/testuser',
            'linkedin_enabled' => true,
            'facebook_url' => 'https://facebook.com/testuser',
            'facebook_enabled' => true, // 4th platform enabled
            'instagram_url' => 'https://instagram.com/testuser',
            'instagram_enabled' => true, // 5th platform enabled
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);

        // Frontend should only display the first 3 platforms based on priority
        $response->assertInertia(fn ($page) =>
            $page->where('profile.social.github', 'https://github.com/testuser')
                 ->where('profile.social.linkedin', 'https://linkedin.com/in/testuser')
                 ->where('profile.social.twitter', 'https://twitter.com/testuser')
                 ->where('profile.social.facebook', null) // Should be null (not included)
                 ->where('profile.social.instagram', null) // Should be null (not included)
        );
    }

    public function test_platform_priority_order_is_respected()
    {
        // Enable platforms in reverse priority order to test priority logic
        $heroSection = HeroSection::factory()->create([
            'instagram_url' => 'https://instagram.com/testuser',
            'instagram_enabled' => true,
            'facebook_url' => 'https://facebook.com/testuser',
            'facebook_enabled' => true,
            'twitter_url' => 'https://twitter.com/testuser',
            'twitter_enabled' => true,
            'linkedin_url' => 'https://linkedin.com/in/testuser',
            'linkedin_enabled' => true,
            'github_url' => 'https://github.com/testuser',
            'github_enabled' => true,
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);

        // Should prioritize GitHub, LinkedIn, Twitter (first 3 in priority order)
        $response->assertInertia(fn ($page) =>
            $page->where('profile.social.github', 'https://github.com/testuser')
                 ->where('profile.social.linkedin', 'https://linkedin.com/in/testuser')
                 ->where('profile.social.twitter', 'https://twitter.com/testuser')
                 ->where('profile.social.facebook', null) // Lower priority, excluded
                 ->where('profile.social.instagram', null) // Lower priority, excluded
        );
    }

    public function test_platforms_without_urls_are_excluded()
    {
        $heroSection = HeroSection::factory()->create([
            'github_url' => 'https://github.com/testuser',
            'github_enabled' => true,
            'twitter_url' => '', // Empty URL
            'twitter_enabled' => true, // Enabled but no URL
            'linkedin_url' => 'https://linkedin.com/in/testuser',
            'linkedin_enabled' => true,
            'facebook_url' => 'https://facebook.com/testuser',
            'facebook_enabled' => true,
            'instagram_url' => 'https://instagram.com/testuser',
            'instagram_enabled' => true,
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);

        // Should skip Twitter (no URL) and include GitHub, LinkedIn, Facebook
        $response->assertInertia(fn ($page) =>
            $page->where('profile.social.github', 'https://github.com/testuser')
                 ->where('profile.social.linkedin', 'https://linkedin.com/in/testuser')
                 ->where('profile.social.twitter', null) // No URL, excluded
                 ->where('profile.social.facebook', 'https://facebook.com/testuser') // Next in priority
                 ->where('profile.social.instagram', null) // 4th platform, excluded
        );
    }
}
