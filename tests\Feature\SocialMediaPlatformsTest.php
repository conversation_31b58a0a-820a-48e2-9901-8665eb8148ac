<?php

namespace Tests\Feature;

use App\Models\HeroSection;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SocialMediaPlatformsTest extends TestCase
{
    use RefreshDatabase;

    public function test_hero_section_has_freelancer_fields()
    {
        $heroSection = HeroSection::factory()->create([
            'freelancer_url' => 'https://freelancer.com/u/testuser',
            'freelancer_enabled' => true,
        ]);

        $this->assertEquals('https://freelancer.com/u/testuser', $heroSection->freelancer_url);
        $this->assertTrue($heroSection->freelancer_enabled);
    }

    public function test_hero_section_has_peopleperhour_fields()
    {
        $heroSection = HeroSection::factory()->create([
            'peopleperhour_url' => 'https://peopleperhour.com/freelancer/testuser',
            'peopleperhour_enabled' => true,
        ]);

        $this->assertEquals('https://peopleperhour.com/freelancer/testuser', $heroSection->peopleperhour_url);
        $this->assertTrue($heroSection->peopleperhour_enabled);
    }

    public function test_hero_section_has_upassign_fields()
    {
        $heroSection = HeroSection::factory()->create([
            'upassign_url' => 'https://upassign.com/yourusername',
            'upassign_enabled' => true,
        ]);

        $this->assertEquals('https://upassign.com/yourusername', $heroSection->upassign_url);
        $this->assertTrue($heroSection->upassign_enabled);
    }

    public function test_welcome_controller_includes_all_social_platforms()
    {
        $heroSection = HeroSection::factory()->create([
            'freelancer_url' => 'https://freelancer.com/u/testuser',
            'freelancer_enabled' => true,
            'peopleperhour_url' => 'https://peopleperhour.com/freelancer/testuser',
            'peopleperhour_enabled' => true,
            'upassign_url' => 'https://upassign.com/yourusername',
            'upassign_enabled' => true,
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);
        
        // Check that the social media URLs are passed to the frontend
        $response->assertInertia(fn ($page) => 
            $page->has('profile.social.freelancer')
                 ->has('profile.social.peopleperhour')
                 ->has('profile.social.upassign')
        );
    }

    public function test_profile_controller_validates_social_media_urls()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'freelancer_url' => null,
            'freelancer_enabled' => false,
            'peopleperhour_url' => null,
            'peopleperhour_enabled' => false,
            'upassign_url' => null,
            'upassign_enabled' => false,
        ]);

        $response = $this->post('/admin/profile/social', [
            'freelancerUrl' => 'https://freelancer.com/u/testuser',
            'freelancerEnabled' => true,
            'peopleperhourUrl' => 'https://peopleperhour.com/freelancer/testuser',
            'peopleperhourEnabled' => true,
            'upassignUrl' => 'https://upassign.com/yourusername',
            'upassignEnabled' => true,
        ]);

        $response->assertRedirect();

        $heroSection->refresh();

        $this->assertEquals('https://freelancer.com/u/testuser', $heroSection->freelancer_url);
        $this->assertTrue($heroSection->freelancer_enabled);
        $this->assertEquals('https://peopleperhour.com/freelancer/testuser', $heroSection->peopleperhour_url);
        $this->assertTrue($heroSection->peopleperhour_enabled);
        $this->assertEquals('https://upassign.com/yourusername', $heroSection->upassign_url);
        $this->assertTrue($heroSection->upassign_enabled);
    }

    public function test_social_media_platforms_can_be_disabled()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $heroSection = HeroSection::factory()->create([
            'freelancer_enabled' => true,
            'peopleperhour_enabled' => true,
            'upassign_enabled' => true,
        ]);

        $response = $this->post('/admin/profile/social', [
            'freelancerEnabled' => false,
            'peopleperhourEnabled' => false,
            'upassignEnabled' => false,
        ]);

        $response->assertRedirect();
        
        $heroSection->refresh();
        
        $this->assertFalse($heroSection->freelancer_enabled);
        $this->assertFalse($heroSection->peopleperhour_enabled);
        $this->assertFalse($heroSection->upassign_enabled);
    }
}
