<?php

// Load Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\HeroSection;

// Get the hero section
$heroSection = HeroSection::getOrCreate();

echo "=== Current State ===\n";
echo "Is Available: " . ($heroSection->is_available ? 'true' : 'false') . "\n";

// Toggle the availability
$newValue = !$heroSection->is_available;
$heroSection->is_available = $newValue;
$heroSection->save();

echo "\n=== Updated State ===\n";
echo "Is Available: " . ($heroSection->is_available ? 'true' : 'false') . "\n";
echo "\nDatabase updated successfully!\n";
echo "Please refresh your homepage to see the changes.\n";

if ($heroSection->is_available) {
    echo "\n✅ Badge should now be VISIBLE on homepage\n";
} else {
    echo "\n❌ Badge should now be HIDDEN on homepage\n";
}
