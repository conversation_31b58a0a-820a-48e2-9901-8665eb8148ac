<?php

// Load Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\HeroSection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

// Get the hero section
$heroSection = HeroSection::getOrCreate();

// Check if public/images/Profile.png exists
$sourcePath = __DIR__ . '/public/images/Profile.png';
if (!File::exists($sourcePath)) {
    echo "Profile.png not found in public/images directory\n";
    exit(1);
}

// Create the avatars directory if it doesn't exist
$avatarsDir = __DIR__ . '/storage/app/public/avatars';
if (!File::exists($avatarsDir)) {
    File::makeDirectory($avatarsDir, 0755, true);
    echo "Created avatars directory\n";
}

// Copy the Profile.png to storage/app/public/avatars
$destinationPath = 'avatars/profile.png';
File::copy($sourcePath, __DIR__ . '/storage/app/public/' . $destinationPath);

// Update the hero section record
$heroSection->avatar = $destinationPath;
$heroSection->save();

echo "Hero section avatar updated successfully\n";